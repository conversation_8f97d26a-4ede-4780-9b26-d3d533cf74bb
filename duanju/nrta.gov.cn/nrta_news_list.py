import requests
import xml.etree.ElementTree as ET
from dataclasses import dataclass
from typing import List

@dataclass
class Record:
    url: str
    title: str
    date: str
    def to_dict(self):
        return {
            'url': self.url,
            'title': self.title,
            'date': self.date
        }

@dataclass
class NRTAResponse:
    totalrecord: int
    totalpage: int
    records: List[Record]
    def to_dict(self):
        return {
            'totalrecord': self.totalrecord,
            'totalpage': self.totalpage,
            'records': [r.to_dict() for r in self.records]
        }

def fetch_nrta_news_list_data(startrecord=1, endrecord=45, perpage=15) -> NRTAResponse:
    url = f"https://www.nrta.gov.cn/module/web/jpage/dataproxy.jsp?startrecord={startrecord}&endrecord={endrecord}&perpage={perpage}"
    payload = {
        'col': "1",
        'webid': "1",
        'path': "https://www.nrta.gov.cn/",
        'columnid': "113",
        'sourceContentType': "1",
        'unitid': "14416",
        'webname': "国家广播电视总局",
        'permissiontype': "0"
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        'Accept': "application/xml, text/xml, */*; q=0.01",
        'Accept-Language': "zh-CN,zh;q=0.9,en;q=0.8",
        'Cache-Control': "no-cache",
        'Origin': "https://www.nrta.gov.cn",
        'Pragma': "no-cache",
        'Referer': "https://www.nrta.gov.cn/col/col113/index.html?uid=14416&pageNum=3",
        'Sec-Fetch-Dest': "empty",
        'Sec-Fetch-Mode': "cors",
        'Sec-Fetch-Site': "same-origin",
        'X-Requested-With': "XMLHttpRequest",
        'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        'sec-ch-ua-mobile': "?0",
        'sec-ch-ua-platform': "\"macOS\"",
        'Cookie': "JSESSIONID=9CD8A685784C2719861D3C2FF56F6929; BIGipServersarft-web=258255040.20480.0000; zycna=mp8ebsQIXcUBAXT2AfMRp813"
    }
    response = requests.post(url, data=payload, headers=headers)
    xml_text = response.text
    root = ET.fromstring(xml_text)
    totalrecord = int(root.findtext('totalrecord', '0'))
    totalpage = int(root.findtext('totalpage', '0'))
    records = []
    recordset_elem = root.find('recordset')
    if recordset_elem is not None:
        for record_elem in recordset_elem.findall('record'):
            cdata = record_elem.text or ''
            # 提取url、title、date
            import re
            match = re.search(r'<a href="([^"]+)"[^>]*title="([^"]+)"[^>]*>(.*?)<span>([\d\-]+)</span>', cdata)
            if match:
                url, title, _, date = match.groups()
                records.append(Record(url=url, title=title, date=date))
    return NRTAResponse(totalrecord=totalrecord, totalpage=totalpage, records=records)
