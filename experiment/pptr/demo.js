import puppeteer from 'puppeteer';

(async () => {
  // 启动浏览器
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();

  // 设置 User-Agent 和 <PERSON>ie
  await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36');
  await page.setExtraHTTPHeaders({
    'cookie': 'gid=yYiKyYSJD474yYiKyYSJ84T18Y7hI1S7WJ7MJTDEKDlIiVq8fvEMW2888qJK48K8KSjqJd0Y; x-user-id-creator.xiaohongshu.com=6479c1f5000000002b00a86d; customerClientId=085725919395539; xsecappid=xhs-pc-web; abRequestId=be1a1d0b-c591-5552-8728-08f48d05d402; a1=196ebbca577ct81tmedzuo9ixjdnr06up2j5fjoml30000367231; webId=5b16c68fb91548b1c780baedebbf49d0; webBuild=4.70.2; acw_tc=0a0bb36c17514378448487695e1c6d4b24498ff929655411c5e735f757291b; web_session=0400698e82fa245640de119a513a4b4974952c; websectiga=29098a4cf41f76ee3f8db19051aaa60c0fc7c5e305572fec762da32d457d76ae; sec_poison_id=dc752ff7-3787-4b42-aee3-0efea56d8fa4; unread={%22ub%22:%22686319800000000023007388%22%2C%22ue%22:%226863db8e000000002203fa5a%22%2C%22uc%22:15}; loadts=1751438388131'
  });

  // 设置窗口大小
  await page.setViewport({ width: 1200, height: 900 });

  // 访问小红书搜索页面
  const url = 'https://www.xiaohongshu.com/search_result?keyword=%25E9%259F%25A9%25E9%259B%25A8%25E5%25BD%25A4&source=web_search_result_notes';
  await page.goto(url, { waitUntil: 'networkidle2' });

  // 等待页面主要内容加载
  await page.waitForSelector('.search-result-container', { timeout: 10000 });

  // 获取所有笔记标题
  const titles = await page.$$eval('.search-note-card .title', nodes => nodes.map(n => n.textContent.trim()));

  // 打印所有标题
  console.log('小红书搜索结果标题:');
  titles.forEach((title, idx) => {
    console.log(`${idx + 1}. ${title}`);
  });

  await browser.close();
})();