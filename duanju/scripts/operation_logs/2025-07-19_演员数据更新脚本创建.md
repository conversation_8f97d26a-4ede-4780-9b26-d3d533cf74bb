# 演员数据更新脚本创建操作记录

## 操作时间
2025-07-19

## 操作目的
将原本的SQL UPDATE语句改为Python脚本形式，实现"先查找后更新，如果找不到则插入"的逻辑，解决部分记录不存在的问题。

## 问题背景
- 原始文件 `duanju/scripts/toscraped.csv` 包含65条UPDATE语句
- 这些UPDATE语句直接更新演员的 `need_scrape` 字段为1
- 但是有些演员记录在数据库中不存在，导致UPDATE操作失败

## 解决方案
创建Python脚本 `update_actors.py`，实现以下逻辑：
1. 读取CSV文件中的演员姓名
2. 对每个演员执行查找操作
3. 如果演员存在：更新 `need_scrape` 字段和 `updated_at` 时间戳
4. 如果演员不存在：插入新记录，设置相关字段

## 创建的文件

### 1. update_actors.py
- **路径**: `duanju/scripts/update_actors.py`
- **功能**: 主要的Python脚本
- **特性**:
  - 使用PyMySQL连接数据库
  - 实现事务处理，确保数据一致性
  - 详细的日志记录
  - 错误处理和回滚机制

### 2. requirements.txt
- **路径**: `duanju/scripts/requirements.txt`
- **内容**: PyMySQL==1.1.0
- **用途**: 管理Python依赖包

### 3. README.md
- **路径**: `duanju/scripts/README.md`
- **内容**: 详细的使用说明和功能介绍

### 4. 操作记录文件夹
- **路径**: `duanju/scripts/operation_logs/`
- **用途**: 统一管理所有操作记录

## 数据库表结构
```sql
CREATE TABLE `duanju_actors` ( 
  `created_at` DATETIME NULL,
  `updated_at` DATETIME NULL,
  `id` BIGINT AUTO_INCREMENT NOT NULL,
  `created_by_id` BIGINT NULL,
  `updated_by_id` BIGINT NULL,
  `name` VARCHAR(255) NULL,
  `platform` VARCHAR(255) NULL,
  `avatar_url` VARCHAR(255) NULL,
  `xhs_id` VARCHAR(255) NULL,
  `weibo_id` VARCHAR(255) NULL,
  `webo_super_index_id` VARCHAR(255) NULL,
  `act_in_counts` BIGINT NULL,
  `desc` TEXT NULL,
  `douyin_id` VARCHAR(255) NULL,
  `gender` VARCHAR(255) NULL,
  `need_scrape` TINYINT NULL,
   PRIMARY KEY (`id`)
)
```

## 数据库连接信息
- 主机: **************
- 端口: 3306
- 用户: root
- 数据库: huawaiyin

## 使用方法
1. 安装依赖: `pip install -r requirements.txt`
2. 运行脚本: `python update_actors.py`

## 预期效果
- 对于已存在的演员：更新 `need_scrape` 字段为1
- 对于不存在的演员：创建新记录，设置 `need_scrape` 为1
- 所有操作都有详细的日志记录
- 确保数据一致性和完整性

## 注意事项
1. 脚本会自动处理CSV文件中演员姓名的空格问题
2. 使用事务确保操作的原子性
3. 详细的错误日志便于问题排查
4. 建议先在测试环境验证功能
