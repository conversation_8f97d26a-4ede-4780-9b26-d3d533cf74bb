# 项目整体分析报告

## 项目概述

**项目名称**: spiders (短剧爬虫项目)  
**项目类型**: 多平台短剧数据爬取与API服务  
**主要技术栈**: Python + FastAPI + Frida + Node.js + Go  
**分析时间**: 2025-07-16  

## 项目架构

### 1. 核心架构
```
spiders/
├── apis/                    # FastAPI Web服务
├── duanju/                  # 各平台短剧爬虫
├── experiment/              # 实验性工具
├── 配置文件                 # Docker、Makefile等
```

### 2. 技术栈分析

#### 后端服务 (Python + FastAPI)
- **框架**: FastAPI 0.1.0
- **服务器**: Uvicorn
- **功能**: 提供短剧相关API服务
- **特点**: 支持CORS、自动文档生成

#### 爬虫技术栈
- **Python**: 主要爬虫语言
- **Frida**: 移动端应用逆向与抓包
- **Node.js + Puppeteer**: 浏览器自动化
- **Go**: 部分服务端功能

#### 移动端技术
- **Taro**: 跨平台小程序开发框架
- **TailwindCSS**: 样式框架

## 主要功能模块

### 1. API服务层 (`apis/`)
- **主服务**: `main.py` - FastAPI应用入口
- **路由模块**: `duanju.py` - 短剧相关API
- **功能**: 
  - 健康检查接口
  - 短剧数据API
  - CORS支持

### 2. 数据爬取层 (`duanju/`)

#### 2.1 皮皮虾平台 (`pipixia/`)
- **主要功能**: 短剧数据爬取与数据库操作
- **核心文件**:
  - `nocobase.duanju.create.py` - 短剧数据创建
  - `api.book.py` - 书籍API调用
  - `api.loadmore.py` - 分页加载
- **数据处理**: JSON格式存储，支持批量创建

#### 2.2 抖音平台 (`douyin/`)
- **功能**: 抖音用户搜索与演员信息获取
- **技术**: 使用Firecrawl API进行搜索
- **数据**: 演员信息JSON存储

#### 2.3 小红书平台 (`xiaohongshu/`)
- **功能**: 演员搜索
- **技术**: HTTP客户端直接调用API
- **特点**: 支持gzip解压缩

#### 2.4 红果平台 (`hongguo/`)
- **功能**: 移动端应用抓包
- **技术**: Frida + r0capture
- **特点**: 
  - SSL协议降级
  - 证书绕过
  - 应用层协议通杀

#### 2.5 国家广电总局 (`nrta.gov.cn/`)
- **功能**: 官方新闻与PDF文档下载
- **数据**: 政策法规相关信息

### 3. 实验工具层 (`experiment/`)

#### 3.1 Puppeteer工具 (`pptr/`)
- **功能**: 浏览器自动化
- **技术**: Node.js + Puppeteer

#### 3.2 r0capture工具 (`r0capture/`)
- **功能**: Android应用层抓包
- **特点**: 
  - 支持Android 7-14
  - 无视证书校验
  - 通杀各种协议

#### 3.3 Reqable工具 (`reqable/`)
- **功能**: HTTP请求分析

## 部署与运维

### 1. 容器化部署
- **基础镜像**: Python 3.8.18
- **多阶段构建**: Dockerfile + Dockerfile.base
- **注册表**: 阿里云容器镜像服务

### 2. 开发工具
- **构建工具**: Makefile
- **代码质量**: black + flake8
- **测试框架**: pytest
- **包管理**: pip + npm + pnpm

### 3. 环境配置
- **Python**: 3.8+
- **Node.js**: 18.x
- **Go**: 1.21
- **数据库**: MySQL

## 数据流向

```
移动端App/网站 → 爬虫工具 → 数据处理 → API服务 → 前端应用
                ↓
            本地存储(JSON/Excel)
                ↓
            数据库(NocoBase)
```

## 项目特点

### 优势
1. **多平台覆盖**: 支持主流短剧平台
2. **技术多样**: 结合多种爬虫技术
3. **模块化设计**: 各平台独立开发
4. **容器化部署**: 便于部署和扩展
5. **API化服务**: 便于前端集成

### 技术亮点
1. **Frida逆向**: 移动端应用深度分析
2. **协议降级**: 绕过SSL限制
3. **多语言混合**: Python+Node.js+Go协同
4. **自动化程度高**: 从数据获取到API服务全流程

## 风险评估

### 技术风险
1. **反爬虫机制**: 各平台可能加强反爬虫
2. **API变更**: 第三方API接口可能变更
3. **法律合规**: 数据爬取的合规性问题

### 运维风险
1. **依赖复杂**: 多技术栈维护成本高
2. **环境依赖**: Frida需要特定Android环境
3. **数据一致性**: 多数据源同步问题

## 改进建议

### 短期优化
1. 添加更完善的错误处理
2. 增加数据验证机制
3. 完善API文档
4. 添加监控告警

### 长期规划
1. 统一数据格式标准
2. 增加数据清洗模块
3. 实现分布式爬取
4. 建立数据质量评估体系
