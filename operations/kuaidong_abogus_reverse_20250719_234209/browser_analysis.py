#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用浏览器自动化来分析真实的 a_bogus 生成过程
通过拦截网络请求来获取真实的参数值和生成逻辑
"""

import json
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

class BrowserABogusAnalyzer:
    """浏览器自动化 a_bogus 分析器"""
    
    def __init__(self):
        self.driver = None
        self.network_logs = []
        self.abogus_patterns = []
        
    def setup_browser(self):
        """设置浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 启用网络日志
        chrome_options.add_argument('--enable-logging')
        chrome_options.add_argument('--log-level=0')
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        # 启用性能日志来捕获网络请求
        chrome_options.add_experimental_option('perfLoggingPrefs', {
            'enableNetwork': True,
            'enablePage': False,
        })
        chrome_options.add_experimental_option('loggingPrefs', {
            'performance': 'ALL'
        })
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def inject_hook_script(self):
        """注入 Hook 脚本来拦截 a_bogus 生成"""
        hook_script = """
        // Hook XMLHttpRequest
        (function() {
            const originalOpen = XMLHttpRequest.prototype.open;
            const originalSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._method = method;
                this._url = url;
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                console.log('XHR Request:', {
                    method: this._method,
                    url: this._url,
                    data: data
                });
                
                // 检查是否包含 a_bogus 参数
                if (this._url && this._url.includes('a_bogus=')) {
                    const urlObj = new URL(this._url);
                    const aBogus = urlObj.searchParams.get('a_bogus');
                    const msToken = urlObj.searchParams.get('msToken');
                    
                    window.capturedParams = window.capturedParams || [];
                    window.capturedParams.push({
                        timestamp: Date.now(),
                        url: this._url,
                        a_bogus: aBogus,
                        msToken: msToken,
                        data: data
                    });
                    
                    console.log('Captured a_bogus:', aBogus);
                }
                
                return originalSend.apply(this, arguments);
            };
            
            // Hook fetch API
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                console.log('Fetch Request:', { url, options });
                
                if (typeof url === 'string' && url.includes('a_bogus=')) {
                    const urlObj = new URL(url);
                    const aBogus = urlObj.searchParams.get('a_bogus');
                    const msToken = urlObj.searchParams.get('msToken');
                    
                    window.capturedParams = window.capturedParams || [];
                    window.capturedParams.push({
                        timestamp: Date.now(),
                        url: url,
                        a_bogus: aBogus,
                        msToken: msToken,
                        options: options
                    });
                    
                    console.log('Captured a_bogus via fetch:', aBogus);
                }
                
                return originalFetch.apply(this, arguments);
            };
            
            // Hook bdms 对象（如果存在）
            if (window.bdms) {
                console.log('Found bdms object:', window.bdms);
                window.originalBdms = window.bdms;
            }
            
            console.log('Hook scripts injected successfully');
        })();
        """
        
        try:
            self.driver.execute_script(hook_script)
            print("✅ Hook 脚本注入成功")
            return True
        except Exception as e:
            print(f"❌ Hook 脚本注入失败: {e}")
            return False
    
    def visit_baike_and_search(self, query="测试"):
        """访问快懂百科并执行搜索"""
        try:
            print(f"🌐 访问快懂百科...")
            self.driver.get("https://www.baike.com/")
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 注入 Hook 脚本
            self.inject_hook_script()
            
            print(f"🔍 搜索关键词: {query}")
            
            # 查找搜索框
            search_selectors = [
                'input[type="text"]',
                'input[placeholder*="搜索"]',
                'input[name="q"]',
                'input[name="query"]',
                '.search-input',
                '#search-input'
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if search_box.is_displayed():
                        break
                except:
                    continue
            
            if search_box:
                print("✅ 找到搜索框")
                search_box.clear()
                search_box.send_keys(query)
                
                # 等待一下让搜索建议出现
                time.sleep(2)
                
                # 尝试触发搜索请求
                search_box.send_keys(" ")  # 触发输入事件
                time.sleep(1)
                search_box.send_keys("\b")  # 删除空格
                time.sleep(2)
                
            else:
                print("❌ 未找到搜索框")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
    
    def capture_network_requests(self, duration=10):
        """捕获网络请求"""
        print(f"📡 开始捕获网络请求 ({duration}秒)...")
        
        start_time = time.time()
        captured_requests = []
        
        while time.time() - start_time < duration:
            try:
                # 获取性能日志
                logs = self.driver.get_log('performance')
                for log in logs:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.requestWillBeSent':
                        request = message['message']['params']['request']
                        url = request.get('url', '')
                        
                        if 'baike.com' in url and ('a_bogus' in url or 'search' in url):
                            captured_requests.append({
                                'timestamp': log['timestamp'],
                                'url': url,
                                'method': request.get('method'),
                                'headers': request.get('headers', {}),
                                'postData': request.get('postData', '')
                            })
                            print(f"🎯 捕获到相关请求: {url[:100]}...")
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"⚠️ 日志获取错误: {e}")
                time.sleep(1)
        
        return captured_requests
    
    def extract_captured_params(self):
        """提取捕获的参数"""
        try:
            captured_params = self.driver.execute_script("return window.capturedParams || [];")
            print(f"📊 捕获到 {len(captured_params)} 个参数组合")
            
            for i, params in enumerate(captured_params):
                print(f"  [{i+1}] a_bogus: {params.get('a_bogus', 'N/A')}")
                print(f"      msToken: {params.get('msToken', 'N/A')[:20]}...")
                print(f"      URL: {params.get('url', 'N/A')[:80]}...")
                print()
            
            return captured_params
            
        except Exception as e:
            print(f"❌ 参数提取失败: {e}")
            return []
    
    def analyze_abogus_patterns(self, captured_params):
        """分析 a_bogus 模式"""
        print("🔍 分析 a_bogus 模式...")
        
        patterns = {
            'lengths': [],
            'charsets': set(),
            'prefixes': [],
            'suffixes': []
        }
        
        for params in captured_params:
            a_bogus = params.get('a_bogus', '')
            if a_bogus:
                patterns['lengths'].append(len(a_bogus))
                patterns['charsets'].update(set(a_bogus))
                if len(a_bogus) >= 4:
                    patterns['prefixes'].append(a_bogus[:4])
                    patterns['suffixes'].append(a_bogus[-4:])
        
        print(f"📏 长度分布: {set(patterns['lengths'])}")
        print(f"🔤 字符集: {''.join(sorted(patterns['charsets']))}")
        print(f"🎯 前缀样本: {patterns['prefixes'][:5]}")
        print(f"🎯 后缀样本: {patterns['suffixes'][:5]}")
        
        return patterns
    
    def save_analysis_results(self, captured_requests, captured_params, patterns):
        """保存分析结果"""
        results = {
            'timestamp': time.time(),
            'captured_requests': captured_requests,
            'captured_params': captured_params,
            'patterns': {
                'lengths': list(set(patterns['lengths'])),
                'charset': ''.join(sorted(patterns['charsets'])),
                'prefixes': patterns['prefixes'][:10],
                'suffixes': patterns['suffixes'][:10]
            }
        }
        
        output_file = "browser_analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 分析结果已保存到: {output_file}")
        return results
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("🧹 浏览器已关闭")

def main():
    """主函数"""
    print("🚀 浏览器自动化 a_bogus 分析")
    print("=" * 50)
    
    analyzer = BrowserABogusAnalyzer()
    
    try:
        # 设置浏览器
        if not analyzer.setup_browser():
            return
        
        # 访问网站并搜索
        if not analyzer.visit_baike_and_search("测试"):
            return
        
        # 捕获网络请求
        captured_requests = analyzer.capture_network_requests(15)
        
        # 提取捕获的参数
        captured_params = analyzer.extract_captured_params()
        
        if captured_params:
            # 分析模式
            patterns = analyzer.analyze_abogus_patterns(captured_params)
            
            # 保存结果
            analyzer.save_analysis_results(captured_requests, captured_params, patterns)
            
            print("\n✅ 分析完成！")
            print("💡 下一步：基于真实参数优化生成算法")
        else:
            print("\n⚠️ 未捕获到 a_bogus 参数")
            print("💡 建议：检查网站是否有变化或需要更复杂的交互")
    
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
    
    finally:
        analyzer.cleanup()

if __name__ == "__main__":
    main()
