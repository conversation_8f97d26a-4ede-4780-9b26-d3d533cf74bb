#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员数据更新脚本
功能：先查找后更新，如果找不到则插入新记录
"""

import pymysql
import csv
import re
import logging
from datetime import datetime
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_actors.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'yuanshu@nb',
    'database': 'huawaiyin',
    'charset': 'utf8mb4',
    'autocommit': False
}

class ActorUpdater:
    def __init__(self):
        self.connection = None
        self.cursor = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**DB_CONFIG)
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def extract_names_from_csv(self, csv_file_path: str) -> List[str]:
        """从CSV文件中提取演员姓名"""
        names = []
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                # 使用正则表达式提取姓名
                pattern = r'where name = "([^"]+)"'
                matches = re.findall(pattern, content)
                names = [name.strip() for name in matches]
                logger.info(f"从CSV文件中提取到 {len(names)} 个演员姓名")
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
        return names
    
    def check_actor_exists(self, name: str) -> Optional[int]:
        """检查演员是否存在，返回ID或None"""
        try:
            sql = "SELECT id FROM duanju_actors WHERE name = %s"
            self.cursor.execute(sql, (name,))
            result = self.cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"查询演员 {name} 失败: {e}")
            return None
    
    def update_actor(self, actor_id: int, name: str) -> bool:
        """更新现有演员的need_scrape字段"""
        try:
            sql = """
            UPDATE duanju_actors 
            SET need_scrape = 1, updated_at = %s 
            WHERE id = %s
            """
            current_time = datetime.now()
            self.cursor.execute(sql, (current_time, actor_id))
            logger.info(f"更新演员 {name} (ID: {actor_id}) 成功")
            return True
        except Exception as e:
            logger.error(f"更新演员 {name} 失败: {e}")
            return False
    
    def insert_actor(self, name: str) -> bool:
        """插入新演员记录"""
        try:
            sql = """
            INSERT INTO duanju_actors 
            (name, need_scrape, created_at, updated_at) 
            VALUES (%s, %s, %s, %s)
            """
            current_time = datetime.now()
            self.cursor.execute(sql, (name, 1, current_time, current_time))
            logger.info(f"插入新演员 {name} 成功")
            return True
        except Exception as e:
            logger.error(f"插入演员 {name} 失败: {e}")
            return False
    
    def process_actor(self, name: str) -> bool:
        """处理单个演员：先查找后更新，找不到则插入"""
        actor_id = self.check_actor_exists(name)
        
        if actor_id:
            # 演员存在，更新记录
            return self.update_actor(actor_id, name)
        else:
            # 演员不存在，插入新记录
            return self.insert_actor(name)
    
    def process_all_actors(self, csv_file_path: str):
        """处理所有演员数据"""
        if not self.connect_db():
            return
        
        try:
            # 提取演员姓名
            names = self.extract_names_from_csv(csv_file_path)
            
            if not names:
                logger.warning("没有找到需要处理的演员姓名")
                return
            
            success_count = 0
            fail_count = 0
            
            # 处理每个演员
            for name in names:
                if self.process_actor(name):
                    success_count += 1
                else:
                    fail_count += 1
            
            # 提交事务
            self.connection.commit()
            logger.info(f"处理完成: 成功 {success_count} 个，失败 {fail_count} 个")
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {e}")
            if self.connection:
                self.connection.rollback()
                logger.info("已回滚事务")
        finally:
            self.close_db()

def main():
    """主函数"""
    csv_file_path = "toscraped.csv"
    
    logger.info("开始处理演员数据更新")
    
    updater = ActorUpdater()
    updater.process_all_actors(csv_file_path)
    
    logger.info("演员数据更新完成")

if __name__ == "__main__":
    main()
