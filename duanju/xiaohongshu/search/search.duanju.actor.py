payload = '{"keyword":"王小亿","search_id":"2f077yo74hvoknuivlabz","biz_type":"web_search_user","request_id":"109378910-1751357708338"}'

import http.client
import gzip

def search_xhs_actor(keyword="王小亿", payload=None, headers=None):
    """
    发送小红书演员搜索请求，返回解码后的响应内容。
    keyword: str，搜索关键词，默认"王小亿"
    payload: str，json字符串，若不为None则直接使用，否则自动构造
    headers: dict，http headers，默认为示例内容。
    """
    if payload is None:
        payload = '{"keyword":"%s","search_id":"2f077yo74hvoknuivlabz","biz_type":"web_search_user","request_id":"109378910-1751357708338"}' % keyword
    if headers is None:
        headers = {
            'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json",
            'pragma': "no-cache",
            'cache-control': "no-cache",
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'x-xray-traceid': "cbe286ec199f4806cc30e8e6e1efeb56",
            'sec-ch-ua-mobile': "?0",
            'x-t': "1751357708365",
            'x-b3-traceid': "a75ab6aa2d1b40bf",
            'x-s-common': "2UQAPsHCPUIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQ+GnPW/MPjNsQhPUHCHdYiqUMIGUM78nHjNsQh+sHCH0c1+AZ1PjHVHdWMH0ijP/D98nQjG9rM+A404eWl4BMS8okMJASk2BkDJdHI+dpIPfiM8fk6JnIAPeZIPeP9+AHAPaHVHdW9H0ijHjIj2eqjwjHjNsQhwsHCHDDAwoQH8B4AyfRI8FS98g+Dpd4daLP3JFSb/BMsn0pSPM87nrldzSzQ2bPAGdb7zgQB8nph8emSy9E0cgk+zSS1qgzianYt8Lzf/LzN4gzaa/+NqMS6qS4HLozoqfQnPbZEp98QyaRSp9P98pSl4oSzcgmca/P78nTTL08z/sVManD9q9z18np/8db8aob7JeQl4epsPrzsagW3tF4ryaRApdz3agYDq7YM47HFqgzkanYMGLSbP9LA/bGIa/+nprSe+9LI4gzVPDbrJg+P4fprLFTALMm7+LSb4d+kpdzt/7b7wrQM498cqBzSpr8g/FSh+bzQygL9nSm7qSmM4epQ4flY/BQdqA+l4oYQ2BpAPp87arS34nMQyFSE8nkdqMD6pMzd8/4SL7bF8aRr+7+rG7mkqBpD8pSUzozQcA8Szb87PDSb/d+/qgzVJfl/4LExpdzQ4fRSy7bFP9+y+7+nJAzdaLp/2LSiz/QH8oQpagYTLrRCJnRQyn+G8pm7zDS9ypHUcd8Azoi7q7Yn4BzQ408S8eq78pSx4LEQz/8S+S4Up/Qc49kQyrkAP9RSqA8r4fpLLozwGML98LzM4ApQ4SSIGAZA8n8n47pPcf4AL7p78LDA+sRQ2BMVq7bFq9bc47SAqFYj/FS98Lz6+npLpd41aLpNqM4fcnLlGMrAanSwqM+I4fp3cS4manSt8pzc4rMQyomrwobF4eQn4b4Q2o8SPMm7PLS3N7Pl4gqMaLpmq9kM4bP3qg4c2Sm7pLS94nTQ2B+U/fpzyrRU/fpk4g4tanYkaFSk+np/Lo41Gflj+rlpcg+gppS3anTgJAzn4rYyLo4eag898gYM4bP3zrTSpflNqMzjJnRQyrRA2e4Sq9Tn4omIqg4wag8m8nkl4MQQyLESpS+y2LDAJ7+DpLkAngbFLDSh+9pLqg4n4Bzg8LSiaBpQc94SpDlm8pc74fp8G08AL7bFqDS3pM4QP9M9JMmFNFSeLS8Qzg8A2r8r4dq6P7+fqg4HaLpzt9QM4eDUGAmALFGM8n8l47mCpd4daL+TzrSkagS0Lo4Uqpm7aFSe89pD/emA2bmFt7kc4rEQzgkOJp8FaFDAzdky+F8HHjIj2eDjw0Hl+erM+AWE+eWVHdWlPsHCPsIj2erlH0ijJfRUJnbVHdF=",
            'content-type': "application/json;charset=UTF-8",
            'x-s': "XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6ImIxOGQxZGFkNDM1ZDMyOGI4NGJiZjBmMzAzMjk5N2RkNmRjZTJmMmQwOGQxZWI5OWUwOTg5MWEwNjE0ZTY3NTdiYzYwYWQzMTI0MzhkMjdhYWU2MWM2ZDY2OTVmN2YyNjc4OTI1ZjdiZjdiNGM0MzUxOWFiN2FhYjUxZTY2NTQ2ZjRmMTk3NzYwOWI0YTc1ZDBiNGQyMmI5ODg0YjIwNGZlODg2ZDA3ZjhhMTUxNTY4MGZlMGQwM2FmYzkwZDQ5ZjllZTVlY2EzMzU1ZDVjMGRmZDRmMWJhNjAzOGVlYmU5ZDAzMzNiNWZkNTdiODU3YTAzODY1ZjBkZGNmZmI5MDA2NzQ1YWM0OWY3MWJjOTRlMGE0MzFmOTcyODZlODEyMjZiZjE0ZDgxZDc1MjJiZTQ0NjM2OThmZDRjZjdlOGFkMzAyN2U0YmY2MjE0YjI1ZjJhNjY3ZjBjMWM0OWM5ZDc5MmU2ZWQxZTNiMDE3MzBjMjQxZTI0NjQ2NWU5ZTk1NmM5MDM5ZGY4MjMxNTlkODRkNDM4OWZjMzhjMGMwYmZiIn0=",
            'x-mns': "unload",
            'origin': "https://www.xiaohongshu.com",
            'sec-fetch-site': "same-site",
            'sec-fetch-mode': "cors",
            'sec-fetch-dest': "empty",
            'referer': "https://www.xiaohongshu.com/",
            'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
            'priority': "u=1, i",
            'Cookie': "gid=yYiKyYSJD474yYiKyYSJ84T18Y7hI1S7WJ7MJTDEKDlIiVq8fvEMW2888qJK48K8KSjqJd0Y; x-user-id-creator.xiaohongshu.com=6479c1f5000000002b00a86d; customerClientId=085725919395539; xsecappid=xhs-pc-web; abRequestId=be1a1d0b-c591-5552-8728-08f48d05d402; a1=196ebbca577ct81tmedzuo9ixjdnr06up2j5fjoml30000367231; webId=5b16c68fb91548b1c780baedebbf49d0; web_session=040069b50d82bdfef83b60bf1e3a4b3b4da6c2; webBuild=4.70.2; unread={%22ub%22:%226861131a000000001c03152d%22%2C%22ue%22:%226849567b000000000303ce72%22%2C%22uc%22:23}; loadts=1751356147958; acw_tc=0a4ad41017513567381568578e7e7ee85eaeedcfb79b9cbc975cc6cae515d6; websectiga=a9bdcaed0af874f3a1431e94fbea410e8f738542fbb02df1e8e30c29ef3d91ac; sec_poison_id=26594178-ee9c-477f-b70a-41a19a48921f"
        }
    conn = http.client.HTTPSConnection("edith.xiaohongshu.com")
    conn.request("POST", "/api/sns/web/v1/search/onebox", payload.encode('utf-8'), headers)
    res = conn.getresponse()
    data = res.read()
    if res.getheader('Content-Encoding') == 'gzip':
        data = gzip.decompress(data)
    return data.decode("utf-8")

# 示例用法：支持命令行传参
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
    else:
        keyword = "王小亿"
    print(search_xhs_actor(keyword=keyword))