import os
import json


import requests
import json

def create_duanju_category(name, cat_id):
    url = "https://data.wansu.tech/api/duanju_categories:create"
    payload = {
        "name": name,
        "cat_id": cat_id,
        "platform": "pipixia"
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Content-Type': "application/json",
        'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
        'authorization': "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NDk3MzUxMDAyMTQsImlhdCI6MTc0OTgyMTUwMCwiZXhwIjoxNzQ5OTA3OTAwLCJqdGkiOiJmNGM1MTk3YS02Y2I3LTQ1YmMtYjhiNC00ZDFiYTJmYTA1MDcifQ.0Iwht03Mp8J9VRcaOptQhxmI60IGBlijfawy0YDzmiQ",
        'origin': "https://data.wansu.tech",
        'priority': "u=1, i",
        'referer': "https://data.wansu.tech/admin/75w5cd28efs",
        'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        'sec-ch-ua-mobile': "?0",
        'sec-ch-ua-platform': "\"macOS\"",
        'sec-fetch-dest': "empty",
        'sec-fetch-mode': "cors",
        'sec-fetch-site': "same-origin",
        'x-authenticator': "basic",
        'x-hostname': "data.wansu.tech",
        'x-locale': "zh-CN",
        'x-role': "root",
        'x-timezone': "+08:00",
        'x-with-acl-meta': "true"
    }
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    return response.text

# 示例调用
# token = "your_token_here"
# print(create_duanju_category("读心术", "cat_660", "pipixia", token))

# 1. 遍历目标目录下所有文件
folder = '/Users/<USER>/Documents/短剧/皮皮虾/列表'
category_map = {}

for filename in os.listdir(folder):
    filepath = os.path.join(folder, filename)
    if not os.path.isfile(filepath):
        continue
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        # 2. 读取 JSON.data[0].feed_data.book_data
        book_list = data.get('data', [{}])[0].get('feed_data', {}).get('book_data', [])
        # 3. 循环书籍信息，提取 category_tags
        for book in book_list:
            category_tags = book.get('book_data', {}).get('category_tags', [])
            # 4. 遍历 category_tags，提取 CategoryId 和 CategoryName
            for tag in category_tags:
                cid = tag.get('CategoryId')
                cname = tag.get('CategoryName')
                if cid and cname and cid not in category_map:
                    category_map[cid] = cname
                    print(f"找到分类: {cid} - {cname}")
                    # 调用 create_duanju_category 创建分类
                    response = create_duanju_category(cname, cid)
                    print(f"创建分类响应: {response}")
    except Exception as e:
        print(f'Error reading {filename}: {e}')

# 5. 输出最终的 map 变量到 json 文件中
output_path = '/Users/<USER>/python/category_map.json'
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(category_map, f, ensure_ascii=False, indent=2)
print(f'Category map saved to {output_path}')

