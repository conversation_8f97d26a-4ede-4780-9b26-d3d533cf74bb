# 技术架构详细分析

## 1. 整体架构设计

### 1.1 分层架构
```
┌─────────────────────────────────────────┐
│              前端层                      │
│         (<PERSON><PERSON>小程序框架)                 │
├─────────────────────────────────────────┤
│              API服务层                   │
│         (FastAPI + Uvicorn)             │
├─────────────────────────────────────────┤
│              业务逻辑层                  │
│         (各平台爬虫模块)                 │
├─────────────────────────────────────────┤
│              数据获取层                  │
│    (Frida + Puppeteer + HTTP Client)   │
├─────────────────────────────────────────┤
│              数据存储层                  │
│      (JSON文件 + NocoBase数据库)        │
└─────────────────────────────────────────┘
```

### 1.2 微服务架构特征
- **服务独立**: 各平台爬虫独立部署
- **技术异构**: 多语言技术栈并存
- **数据解耦**: 统一数据格式接口

## 2. 核心技术组件分析

### 2.1 FastAPI服务框架

#### 技术选型优势
- **高性能**: 基于Starlette和Pydantic
- **自动文档**: OpenAPI/Swagger自动生成
- **类型安全**: Python类型提示支持
- **异步支持**: 原生async/await

#### 架构实现
```python
# 主应用结构
app = FastAPI(
    title="FastAPI 短剧服务",
    description="短剧相关 API 服务，支持多业务扩展",
    version="0.1.0"
)

# 中间件配置
app.add_middleware(CORSMiddleware, ...)

# 路由模块化
app.include_router(duanju.router)
```

#### 扩展性设计
- **路由模块化**: 支持业务模块独立开发
- **中间件机制**: 统一处理跨域、认证等
- **事件钩子**: 启动时路由注册和日志初始化

### 2.2 Frida逆向工程框架

#### 核心能力
- **动态插桩**: 运行时代码注入
- **协议分析**: SSL/TLS协议降级
- **API Hook**: 系统调用拦截

#### 技术实现
```python
# SSL协议降级Hook
hook_script = """
Interceptor.attach(Module.findExportByName(null, "SSL_CTX_set_min_proto_version"), {
    onEnter: function (args) {
        args[1] = 0x0301; // 强制TLS 1.0
    }
});
"""
```

#### 应用场景
- **移动端抓包**: 绕过证书校验
- **API逆向**: 分析加密参数
- **协议分析**: 深度包检测

### 2.3 数据获取技术栈

#### 2.3.1 HTTP客户端爬虫
```python
# 小红书API调用示例
conn = http.client.HTTPSConnection("edith.xiaohongshu.com")
conn.request("POST", "/api/sns/web/v1/search/onebox", payload, headers)
```

**特点**:
- 直接API调用
- 支持gzip解压
- 自定义请求头

#### 2.3.2 浏览器自动化
```javascript
// Puppeteer自动化
const puppeteer = require('puppeteer');
const browser = await puppeteer.launch();
```

**优势**:
- 完整浏览器环境
- JavaScript渲染支持
- 反检测能力强

#### 2.3.3 移动端逆向
```python
# ADB设备控制
client = AdbClient(host="127.0.0.1", port=5037)
device = client.devices()[0]
device.shell(f"am start -n {package}/{activity}")
```

**能力**:
- 设备自动化控制
- 应用启动管理
- 数据包拦截

## 3. 数据处理架构

### 3.1 数据流设计
```
原始数据 → 数据清洗 → 格式标准化 → API输出
    ↓
  本地缓存 → 数据库存储 → 数据分析
```

### 3.2 数据存储策略

#### JSON文件存储
- **优势**: 开发简单、调试方便
- **适用**: 小规模数据、临时存储
- **示例**: `duanju_actors.json`

#### NocoBase数据库
- **优势**: 结构化存储、查询能力强
- **适用**: 生产数据、关系数据
- **API**: RESTful接口

### 3.3 数据同步机制
```python
# 批量创建机制
created_book_ids = set()
save_created_book_ids_counter = 0

def save_created_book_ids(force=False):
    global save_created_book_ids_counter
    if save_created_book_ids_counter >= 10 or force:
        # 周期性保存逻辑
```

## 4. 部署架构

### 4.1 容器化设计

#### 多阶段构建
```dockerfile
# 基础镜像
FROM registry.cn-shanghai.aliyuncs.com/jony4base/python:3.8.18

# 业务镜像
FROM registry.cn-shanghai.aliyuncs.com/jony4/fastapi-app-base:latest AS base
```

#### 优势
- **镜像复用**: 基础镜像共享
- **构建优化**: 分层缓存
- **部署一致**: 环境标准化

### 4.2 开发工具链

#### Makefile自动化
```makefile
all: baseimage image install fmt lint test
install: pip install -r requirements.txt
fmt: black .
lint: flake8 .
test: pytest
```

#### 特点
- **一键构建**: 完整CI/CD流程
- **代码质量**: 自动格式化和检查
- **测试集成**: 自动化测试

## 5. 性能与扩展性

### 5.1 性能优化策略
- **异步处理**: FastAPI异步支持
- **连接池**: HTTP客户端复用
- **缓存机制**: 数据本地缓存
- **批量处理**: 数据批量操作

### 5.2 扩展性设计
- **模块化**: 平台独立开发
- **配置化**: 环境变量配置
- **插件化**: 中间件机制
- **微服务**: 服务独立部署

### 5.3 监控与运维
- **日志系统**: 结构化日志
- **健康检查**: API健康监控
- **错误处理**: 异常捕获机制
- **性能监控**: 请求响应时间

## 6. 安全性考虑

### 6.1 数据安全
- **访问控制**: API权限管理
- **数据加密**: 敏感信息保护
- **审计日志**: 操作记录追踪

### 6.2 网络安全
- **HTTPS**: 传输加密
- **CORS**: 跨域访问控制
- **防爬虫**: 请求频率限制

### 6.3 合规性
- **数据使用**: 合理使用原则
- **隐私保护**: 个人信息脱敏
- **法律合规**: 相关法规遵守

## 7. 技术债务与改进方向

### 7.1 当前技术债务
- **错误处理**: 异常处理不够完善
- **测试覆盖**: 单元测试不足
- **文档完善**: API文档需要补充
- **监控告警**: 缺少完整监控体系

### 7.2 改进建议
- **统一标准**: 数据格式标准化
- **服务治理**: 微服务管理平台
- **自动化**: CI/CD流程完善
- **可观测性**: 全链路监控
