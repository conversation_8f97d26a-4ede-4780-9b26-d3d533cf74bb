import os
import json
import glob
import requests
import concurrent.futures

def download_cover(args):
    chapter_thumb_url, cover_path = args
    if os.path.exists(cover_path):
        print(f'封面图已存在: {cover_path}')
        return
    try:
        img_resp = requests.get(chapter_thumb_url, timeout=10)
        if img_resp.status_code == 200:
            with open(cover_path, 'wb') as img_f:
                img_f.write(img_resp.content)
            print(f'已保存封面图: {cover_path}')
        else:
            print(f'图片下载失败: {chapter_thumb_url} 状态码: {img_resp.status_code}')
    except Exception as e:
        print(f'下载图片出错: {chapter_thumb_url} 错误: {e}')

DETAIL_DIR = '/Users/<USER>/Documents/短剧/皮皮虾/详情'
COVER_DIR = '/Users/<USER>/Documents/短剧/皮皮虾/详情-封面图'
os.makedirs(COVER_DIR, exist_ok=True)

json_files = glob.glob(os.path.join(DETAIL_DIR, '*.json'))
download_tasks = []
for file_path in json_files:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        book_id = os.path.splitext(os.path.basename(file_path))[0]
        dictionary_list = data.get('data', {}).get('dictionary', {}).get('list', [])
        for dic_item in dictionary_list:
            chapter_thumb_url = dic_item.get('chapter_thumb_url')
            item_id = dic_item.get('item_id')
            if chapter_thumb_url and item_id:
                cover_path = os.path.join(COVER_DIR, f'{book_id}_{item_id}.webp')
                download_tasks.append((chapter_thumb_url, cover_path))
    except Exception as e:
        print(f'解析详情文件或收集封面图任务出错: {file_path} {e}')
        continue

with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
    list(executor.map(download_cover, download_tasks))
print('所有封面图下载完成！')
