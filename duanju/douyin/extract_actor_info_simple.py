#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员信息提取脚本（简化版）
从 search_user_results.json 中提取有用信息，针对 duanju_actors_need_scrape.json 中的演员
使用正则表达式和关键词匹配提取信息
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

def load_target_actors() -> Dict[str, str]:
    """加载目标演员列表"""
    try:
        with open("duanju_actors_need_scrape.json", "r", encoding="utf-8") as f:
            actors = json.load(f)
            return {str(actor["id"]): actor["name"] for actor in actors}
    except Exception as e:
        print(f"读取目标演员列表失败: {e}")
        return {}

def load_search_results() -> Dict[str, Any]:
    """加载搜索结果"""
    try:
        with open("search_user_results.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取搜索结果失败: {e}")
        return {}

def extract_social_media_ids(text: str, url: str) -> Dict[str, Optional[str]]:
    """从文本和URL中提取社交媒体ID"""
    result = {
        "xiaohongshu_id": None,
        "weibo_id": None,
        "weibo_super_topic_id": None,
        "douyin_id": None
    }
    
    # 小红书ID提取
    if "xiaohongshu.com" in url or "xhs.com" in url:
        # 从URL中提取用户ID
        xhs_match = re.search(r'user/([a-zA-Z0-9]+)', url)
        if xhs_match:
            result["xiaohongshu_id"] = xhs_match.group(1)
        else:
            # 从文本中查找小红书相关信息
            xhs_patterns = [
                r'小红书[：:]\s*([a-zA-Z0-9_\u4e00-\u9fff]+)',
                r'小红书ID[：:]\s*([a-zA-Z0-9_]+)',
                r'@([a-zA-Z0-9_\u4e00-\u9fff]+).*小红书'
            ]
            for pattern in xhs_patterns:
                match = re.search(pattern, text)
                if match:
                    result["xiaohongshu_id"] = match.group(1)
                    break
    
    # 微博ID提取
    if "weibo.com" in url:
        # 从URL中提取用户ID
        weibo_match = re.search(r'weibo\.com/u/(\d+)', url)
        if weibo_match:
            result["weibo_id"] = weibo_match.group(1)
        else:
            weibo_match = re.search(r'weibo\.com/([a-zA-Z0-9_]+)', url)
            if weibo_match:
                result["weibo_id"] = weibo_match.group(1)
        
        # 微博超话ID
        if "超话" in text or "supertopic" in url:
            topic_match = re.search(r'超话[：:]?\s*([a-zA-Z0-9_\u4e00-\u9fff]+)', text)
            if topic_match:
                result["weibo_super_topic_id"] = topic_match.group(1)
    
    # 抖音ID提取
    if "douyin.com" in url or "抖音" in text:
        douyin_patterns = [
            r'抖音[：:]\s*([a-zA-Z0-9_\u4e00-\u9fff]+)',
            r'抖音ID[：:]\s*([a-zA-Z0-9_]+)',
            r'@([a-zA-Z0-9_\u4e00-\u9fff]+).*抖音',
            r'douyin\.com/user/([a-zA-Z0-9_]+)'
        ]
        for pattern in douyin_patterns:
            match = re.search(pattern, text)
            if match:
                result["douyin_id"] = match.group(1)
                break
    
    return result

def extract_actor_bio(actor_name: str, search_data: List[Dict]) -> str:
    """提取演员简介"""
    bio_parts = []
    seen_descriptions = set()
    
    for item in search_data:
        title = item.get("title", "")
        description = item.get("description", "")
        
        # 优先处理百度百科的信息
        if "百度百科" in title and description:
            if description not in seen_descriptions:
                bio_parts.append(description)
                seen_descriptions.add(description)
        
        # 处理其他有价值的描述
        elif description and len(description) > 30:
            # 过滤掉一些无关的描述
            if not any(keyword in description for keyword in ["广告", "推广", "下载", "APP", "游戏"]):
                if description not in seen_descriptions:
                    bio_parts.append(description)
                    seen_descriptions.add(description)
    
    # 合并并去重
    if bio_parts:
        # 取前3个最有价值的描述
        return " ".join(bio_parts[:3])
    
    return ""

def extract_sources(search_data: List[Dict]) -> List[str]:
    """提取信息来源"""
    sources = []
    for item in search_data:
        url = item.get("url", "")
        if url:
            # 只保留主要的信息源
            if any(domain in url for domain in ["baidu.com", "weibo.com", "xiaohongshu.com", "douyin.com"]):
                sources.append(url)
    
    return list(set(sources))  # 去重

def process_actor_info(actor_name: str, search_data: List[Dict]) -> Dict[str, Any]:
    """处理单个演员的信息"""
    result = {
        "xiaohongshu_id": None,
        "weibo_id": None,
        "weibo_super_topic_id": None,
        "douyin_id": None,
        "actor_bio": "",
        "sources": []
    }
    
    all_text = ""
    all_urls = []
    
    # 收集所有文本和URL
    for item in search_data:
        title = item.get("title", "")
        description = item.get("description", "")
        url = item.get("url", "")
        markdown = item.get("markdown", "")
        
        all_text += f"{title} {description} {markdown} "
        if url:
            all_urls.append(url)
    
    # 从每个URL和对应的文本中提取社交媒体信息
    for item in search_data:
        title = item.get("title", "")
        description = item.get("description", "")
        url = item.get("url", "")
        text = f"{title} {description}"
        
        social_ids = extract_social_media_ids(text, url)
        
        # 更新结果（优先保留第一个找到的有效ID）
        for key, value in social_ids.items():
            if value and not result[key]:
                result[key] = value
    
    # 提取演员简介
    result["actor_bio"] = extract_actor_bio(actor_name, search_data)
    
    # 提取信息来源
    result["sources"] = extract_sources(search_data)
    
    return result

def process_actors():
    """处理所有演员信息"""
    target_actors = load_target_actors()
    search_results = load_search_results()
    
    if not target_actors:
        print("没有找到目标演员列表")
        return
    
    if not search_results:
        print("没有找到搜索结果")
        return
    
    extracted_info = {}
    processed_count = 0
    
    print(f"开始处理 {len(target_actors)} 个演员的信息...")
    
    for actor_id, actor_name in target_actors.items():
        if actor_id in search_results:
            search_data = search_results[actor_id]
            
            if search_data.get("success") and search_data.get("data"):
                print(f"处理演员: {actor_name} (ID: {actor_id})")
                
                # 提取信息
                info = process_actor_info(actor_name, search_data["data"])
                
                extracted_info[actor_id] = {
                    "name": actor_name,
                    "xiaohongshu_id": info["xiaohongshu_id"],
                    "weibo_id": info["weibo_id"],
                    "weibo_super_topic_id": info["weibo_super_topic_id"],
                    "douyin_id": info["douyin_id"],
                    "actor_bio": info["actor_bio"],
                    "sources": info["sources"],
                    "extracted_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                processed_count += 1
                
                # 显示提取到的信息摘要
                summary = []
                if info["xiaohongshu_id"]:
                    summary.append(f"小红书: {info['xiaohongshu_id']}")
                if info["weibo_id"]:
                    summary.append(f"微博: {info['weibo_id']}")
                if info["douyin_id"]:
                    summary.append(f"抖音: {info['douyin_id']}")
                if info["actor_bio"]:
                    summary.append(f"简介: {info['actor_bio'][:50]}...")
                
                if summary:
                    print(f"  提取到: {', '.join(summary)}")
                else:
                    print(f"  未提取到有效信息")
                    
            else:
                print(f"演员 {actor_name} (ID: {actor_id}) 的搜索结果无效")
        else:
            print(f"演员 {actor_name} (ID: {actor_id}) 没有搜索结果")
    
    # 保存结果
    output_file = "extracted_actor_info_simple.json"
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(extracted_info, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 信息提取完成！")
        print(f"处理了 {processed_count} 个演员")
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果失败: {e}")
    
    # 创建统计报告
    create_summary_report(extracted_info)

def create_summary_report(extracted_info: Dict[str, Any]):
    """创建统计报告"""
    total_actors = len(extracted_info)
    has_xiaohongshu = sum(1 for info in extracted_info.values() if info.get("xiaohongshu_id"))
    has_weibo = sum(1 for info in extracted_info.values() if info.get("weibo_id"))
    has_weibo_topic = sum(1 for info in extracted_info.values() if info.get("weibo_super_topic_id"))
    has_douyin = sum(1 for info in extracted_info.values() if info.get("douyin_id"))
    has_bio = sum(1 for info in extracted_info.values() if info.get("actor_bio"))
    
    report = {
        "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_actors_processed": total_actors,
        "statistics": {
            "xiaohongshu_accounts_found": has_xiaohongshu,
            "weibo_accounts_found": has_weibo,
            "weibo_super_topics_found": has_weibo_topic,
            "douyin_accounts_found": has_douyin,
            "actor_bios_extracted": has_bio
        },
        "coverage_rates": {
            "xiaohongshu_coverage": f"{(has_xiaohongshu/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_coverage": f"{(has_weibo/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_topic_coverage": f"{(has_weibo_topic/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "douyin_coverage": f"{(has_douyin/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "bio_coverage": f"{(has_bio/total_actors*100):.1f}%" if total_actors > 0 else "0%"
        }
    }
    
    # 保存统计报告
    report_file = "extraction_summary_report_simple.json"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"统计报告已保存到: {report_file}")
    except Exception as e:
        print(f"保存统计报告失败: {e}")
    
    # 打印统计信息
    print(f"\n📊 提取统计:")
    print(f"总处理演员数: {total_actors}")
    print(f"小红书账号: {has_xiaohongshu} ({report['coverage_rates']['xiaohongshu_coverage']})")
    print(f"微博账号: {has_weibo} ({report['coverage_rates']['weibo_coverage']})")
    print(f"微博超话: {has_weibo_topic} ({report['coverage_rates']['weibo_topic_coverage']})")
    print(f"抖音账号: {has_douyin} ({report['coverage_rates']['douyin_coverage']})")
    print(f"演员简介: {has_bio} ({report['coverage_rates']['bio_coverage']})")

if __name__ == "__main__":
    process_actors()
