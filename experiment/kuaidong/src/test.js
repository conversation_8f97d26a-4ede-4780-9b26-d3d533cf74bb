import { BaikeSearcher } from './searcher.js';
import logger from './logger.js';

async function runTests() {
  console.log('🧪 开始运行测试...\n');
  
  const searcher = new BaikeSearcher({
    headless: true, // 测试时使用无头模式
    timeout: 30000
  });

  const testCases = [
    '王小亿',
    '中国短剧演员',
    '演员',
    '不存在的关键词12345'
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (let i = 0; i < testCases.length; i++) {
    const keyword = testCases[i];
    console.log(`📋 测试 ${i + 1}/${totalTests}: 搜索 "${keyword}"`);
    
    try {
      const results = await searcher.search(keyword);
      
      if (Array.isArray(results)) {
        console.log(`✅ 测试通过 - 返回 ${results.length} 个结果`);
        
        if (results.length > 0) {
          const firstResult = results[0];
          console.log(`   📄 第一个结果: ${JSON.stringify(firstResult, null, 2)}`);
          
          // 验证结果格式
          if (firstResult.DocID && firstResult.ID && 
              typeof firstResult.Subtitle === 'string' && 
              typeof firstResult.Title === 'string') {
            console.log(`   ✅ 结果格式正确`);
          } else {
            console.log(`   ⚠️  结果格式可能不完整`);
          }
        }
        
        passedTests++;
      } else {
        console.log(`❌ 测试失败 - 返回结果不是数组`);
      }
      
    } catch (error) {
      console.log(`❌ 测试失败 - ${error.message}`);
      logger.logError('test', error, { keyword, testIndex: i });
    }
    
    console.log(''); // 空行分隔
    
    // 测试间隔
    if (i < testCases.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  await searcher.close();
  
  console.log(`\n🎯 测试总结:`);
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log(`🎉 所有测试通过!`);
  } else {
    console.log(`⚠️  有 ${totalTests - passedTests} 个测试失败`);
  }
}

// 性能测试
async function performanceTest() {
  console.log('\n⚡ 开始性能测试...\n');
  
  const searcher = new BaikeSearcher({
    headless: true,
    timeout: 30000
  });

  const keyword = '王小亿';
  const iterations = 3;
  const times = [];

  for (let i = 0; i < iterations; i++) {
    console.log(`🔄 性能测试 ${i + 1}/${iterations}`);
    
    const startTime = Date.now();
    
    try {
      const results = await searcher.search(keyword);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      times.push(duration);
      console.log(`   ⏱️  耗时: ${duration}ms, 结果数: ${results.length}`);
      
    } catch (error) {
      console.log(`   ❌ 失败: ${error.message}`);
    }
    
    if (i < iterations - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  await searcher.close();
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`\n📊 性能统计:`);
    console.log(`   平均耗时: ${avgTime.toFixed(0)}ms`);
    console.log(`   最快: ${minTime}ms`);
    console.log(`   最慢: ${maxTime}ms`);
  }
}

// 批量测试
async function batchTest() {
  console.log('\n📦 开始批量测试...\n');
  
  const searcher = new BaikeSearcher({
    headless: true,
    timeout: 30000
  });

  const keywords = ['王小亿', '演员', '中国'];
  
  try {
    const results = await searcher.batchSearch(keywords);
    
    console.log('📋 批量测试结果:');
    for (const [keyword, result] of Object.entries(results)) {
      if (result) {
        console.log(`   ✅ ${keyword}: ${result.length} 个结果`);
      } else {
        console.log(`   ❌ ${keyword}: 失败`);
      }
    }
    
  } catch (error) {
    console.log(`❌ 批量测试失败: ${error.message}`);
  } finally {
    await searcher.close();
  }
}

// 主测试函数
async function main() {
  try {
    await runTests();
    await performanceTest();
    await batchTest();
    
    console.log('\n🎉 所有测试完成!');
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);
    logger.logError('test_main', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
