import requests
import os
import time
import json
import random
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

url = "https://api.fanqiesdk.com/api/novel/channel/cell/stream/loadmore/mix/v1?batch_n=5&request_tag_from=lynx&sdk_type=3&auto_play_cell_book_num=2&from_scene=401&selected_items=cat_1051&parent_enterfrom=playlet_category&scm_version=1.0.0.725&iid=1725565850678041&device_id=4346801548690859&ac=wifi&channel=oppo_1319_tr_64&aid=1319&app_name=super&version_code=556&version_name=5.5.6&device_platform=android&os=android&ssmix=a&device_type=IN2020&device_brand=OnePlus&language=zh&os_api=31&os_version=12&manifest_version_code=556&resolution=1440*2980&dpi=640&update_version_code=55650&_rticket=1749739673246&cdid=f5b36d47-dc78-4198-8f6a-5f5d6edc3614&recommend_disable=0&carrier_region=CN&app_language=ZH&app_region=CN&sys_region=CN&update_install_update_version=55650&time_zone=Asia%2FShanghai&last_channel=&last_update_version_code=0&ts=1749739673"

headers = {
  'User-Agent': "com.sup.android.superb/556 (Linux; U; Android 12; zh_CN; IN2020; Build/RKQ1.211119.001; Cronet/TTNetVersion:b714bfef 2024-09-13 QuicVersion:c459d547 2024-08-27)",
  'Accept-Encoding': "gzip, deflate",
  'x-vc-bdturing-sdk-version': "3.7.2.cn",
  'x-tt-trace-id': "00-649c98420df71649a3805ab6c2de0527-649c98420df71649-01",
  'X-Argus': "mehKaA==",
  'X-Gorgon': "8404602b0000d31e4be1c8d91b170bd79cc2652c37b93d3a8c87",
  'X-Helios': "6y0VVBmxr+BOiQTJk+ewW1G3OSpVSQSeYEBhfMoPbupcihSz",
  'X-Khronos': "1749739673",
  'X-Ladon': "aEromQ==",
  'X-Medusa': "muhKaG4AFZJOP5ZTT8KCGM4nK3DnOAAB9qFNBAMDAUSGOKjiTH9qb6a8HeMFOSmixDtHTuQiP1QmnWyNASyhpLbQCcoT8VZMCSF0yGcujPC7znSBE3AfhgYxPicEyc7R5KVKOYMWImFfHLMPqGpjvPaEe1kOAobIhwop4OdSJUzDRWmzMsudOOOY/yTkMPQ3JGH56y3R8u/nOOLAyV7pBIgoNhLRDGuudQc3nL5PBVR0bGEWfbTUNtZq4l4/zLesmScV485RAA7p0LOrCV7fyLPbRLjn9dRx58bnzVwuzqjQL+Z3jAjFeVnmegd4u3JHq2w9cAOTA91HSN+9X9SfSMzUefk+ehA0ULuyru9W9VuV+V0I9t/NmplJatLmU208CEc8318jKhEVCkdJA7IZrrB8IvT3aUtQTLCMQOvyO6ZA69lTOHm9HK0yEJgV2vtqe9jdTPRbsybRyrWvF85HpGjQJpsTAp7KFr3SAooFbrBjqeLHDWgaA6SxJNM/183Ic4ym6JHhfadW30BkFuqTqYneYtEssA8DG4zbLQnPDyNDdXGHDjIE5VHVyEk/jcktrgp0yDQ6dTZDb4INMSXHeCA8HnA09s8QOns4FFoBjMPs+fJRzS5aI1F0hA6RAcKzrJtndKFik7pQx2nASmkEM53hFc3649MPK4Yq/s8rUWafGPMekSsk46ttnmOvtKv3vGSO4nkJd6Hour2v5a7Rus3r6FJ00TNFLnuaQP1MLFPrHm+UGUG/dfJBwIUGvPy1JRXsGyG8W+8/y8qxQaGRUKeZZLIs3YDqtk0rQGMWCjDJ5wnBylyqEveX8lAOFnF8s2N9LjpTvTaAMJL793KCTgaM/0fj9NngIsPbHjdelFCUFql/+/mJwvMidB6Rr5/TicMm4RB22GzbGEbm/mfoppUoOYJIO4O/+IH0XetdftW04l7kAIGSUzx71+9mk7XrsmJgIhPkCoyi4gzZdx3g3/uyy76ihf/7V3r/+1d6lRQ="
}

with open("cat.json", "r", encoding="utf-8") as f:
    cat_data = json.load(f)
    cat_ids = [item["selector_item_id"] for item in cat_data["items"]]

for selected_items in cat_ids:
    for batch_n in range(1, 500):
        dynamic_url = url.replace('batch_n=5', f'batch_n={batch_n}').replace('selected_items=cat_1051', f'selected_items={selected_items}')
        try:
            response = requests.get(dynamic_url, headers=headers)
            print(f"selected_items={selected_items}, batch_n={batch_n} response:")
            try:
                data = response.json()
                # Check if has_more is false
                data0 = data.get("data", [])[0] if data.get("data") else None
                if data0:
                    feed_data = data0.get("feed_data", {})
                    if not feed_data.get("has_more", True):
                        print(f"No more data for selected_items={selected_items}, stopping.")
                        break
            except Exception as e:
                print(f"Error parsing JSON: {e}")
                data = response.text
            directory = "/Users/<USER>/Documents/短剧/皮皮虾/列表"
            os.makedirs(directory, exist_ok=True)
            prefix = f"{selected_items}-{batch_n}"
            exists = any(name.startswith(prefix) for name in os.listdir(directory))
            if exists:
                print(f"Skip: {prefix} already exists.")
                continue
            print(f"Saving file for {selected_items}, batch_n={batch_n}")
            timestamp = int(time.time())
            filename = os.path.join(directory, f"{selected_items}-{batch_n}-{timestamp}.json")
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"Saved file: {filename}")
            # 下载封面图
            cover_dir = "/Users/<USER>/Documents/短剧/皮皮虾/封面图"
            os.makedirs(cover_dir, exist_ok=True)
            try:
                # 兼容 response.text/response.content
                if isinstance(response.content, bytes):
                    body_json = json.loads(response.content.decode("utf-8"))
                else:
                    body_json = json.loads(str(response.content))
                data0 = body_json.get("data", [])[0] if body_json.get("data") else None
                if data0:
                    feed_data = data0.get("feed_data", {})
                    book_data_list = feed_data.get("book_data", [])
                    for item in book_data_list:
                        book = item.get("book_data", {})
                        thumb_url = book.get("thumb_url")
                        book_id = book.get("book_id")
                        if thumb_url and book_id:
                            cover_path = os.path.join(cover_dir, f"{book_id}.webp")
                            if not os.path.exists(cover_path):
                                try:
                                    resp = requests.get(thumb_url, timeout=10, verify=False)
                                    if resp.status_code == 200:
                                        with open(cover_path, "wb") as imgf:
                                            imgf.write(resp.content)
                                    else:
                                        print(f"Failed to download cover: {thumb_url}, status: {resp.status_code}")
                                except Exception as e:
                                    print(f"Download error for {thumb_url}: {e}")
                            else:
                                print(f"Cover already exists: {cover_path}")
            except Exception as e:
                print(f"JSON parse or download error: {e}")
        except (requests.exceptions.SSLError, urllib3.exceptions.MaxRetryError) as e:
            print(f"SSL Error for {selected_items}, batch_n={batch_n}: {e}")
            break  # Break inner loop to continue with next cat_id
        except Exception as e:
            print(f"Unexpected error for {selected_items}, batch_n={batch_n}: {e}")
            continue
