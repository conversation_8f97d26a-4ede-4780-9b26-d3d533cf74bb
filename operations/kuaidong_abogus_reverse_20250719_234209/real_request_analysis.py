#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析真实的快懂百科请求，对比我们生成的 a_bogus 参数
通过分析真实请求的特征来改进算法
"""

import requests
import json
import time
import re
import base64
import hashlib
from urllib.parse import urlparse, parse_qs

class RealRequestAnalyzer:
    """真实请求分析器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.real_abogus_samples = []
        
        # 设置真实的浏览器请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': 'https://www.baike.com',
            'Referer': 'https://www.baike.com/',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    def analyze_real_abogus_from_curl(self):
        """分析用户提供的真实 a_bogus 参数"""
        # 用户提供的真实 a_bogus 值
        real_abogus = "Y6UQXc2aMsm1r8leBXkz9SaovZm0YW5IgZENcvvqGtw6"
        
        print(f"🔍 分析真实的 a_bogus: {real_abogus}")
        
        # 分析特征
        analysis = {
            'length': len(real_abogus),
            'charset': set(real_abogus),
            'has_uppercase': any(c.isupper() for c in real_abogus),
            'has_lowercase': any(c.islower() for c in real_abogus),
            'has_digits': any(c.isdigit() for c in real_abogus),
            'has_special': any(c in '+/-_=' for c in real_abogus),
            'base64_like': self.is_base64_like(real_abogus),
            'possible_encoding': self.analyze_encoding(real_abogus)
        }
        
        print(f"📊 分析结果:")
        print(f"   长度: {analysis['length']}")
        print(f"   字符集: {''.join(sorted(analysis['charset']))}")
        print(f"   包含大写: {analysis['has_uppercase']}")
        print(f"   包含小写: {analysis['has_lowercase']}")
        print(f"   包含数字: {analysis['has_digits']}")
        print(f"   包含特殊字符: {analysis['has_special']}")
        print(f"   类似 Base64: {analysis['base64_like']}")
        print(f"   可能的编码: {analysis['possible_encoding']}")
        
        return analysis
    
    def is_base64_like(self, s):
        """检查字符串是否像 Base64 编码"""
        base64_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
        return all(c in base64_chars for c in s)
    
    def analyze_encoding(self, s):
        """分析可能的编码方式"""
        encodings = []
        
        # 检查是否是标准 Base64
        try:
            # 尝试添加填充
            padded = s + '=' * (4 - len(s) % 4) if len(s) % 4 else s
            decoded = base64.b64decode(padded)
            encodings.append(f"Base64 -> {decoded.hex()}")
        except:
            pass
        
        # 检查是否是 URL 安全的 Base64
        try:
            # 替换 URL 安全字符
            url_safe = s.replace('-', '+').replace('_', '/')
            padded = url_safe + '=' * (4 - len(url_safe) % 4) if len(url_safe) % 4 else url_safe
            decoded = base64.b64decode(padded)
            encodings.append(f"URL-Safe Base64 -> {decoded.hex()}")
        except:
            pass
        
        return encodings
    
    def test_with_real_cookies(self):
        """使用真实的 Cookie 测试请求"""
        print("🍪 测试使用真实 Cookie 的请求...")
        
        # 从用户的 curl 命令中提取的 Cookie
        cookies = {
            'ttwid': '1|F5vqe9pfJboQWRSnvs4FvdDYdg6HzaK3fBqh5lv3a0M|1752937842|cdbce3fd7ed001c2a944f25731dd8b6b035bc22531120217dfbe8b1224676aec',
            'ttwid_ss': '1|F5vqe9pfJboQWRSnvs4FvdDYdg6HzaK3fBqh5lv3a0M|1752937842|cdbce3fd7ed001c2a944f25731dd8b6b035bc22531120217dfbe8b1224676aec',
            'COOKIE_IS_LOGIN_FLAG': '0',
            'first_screen_node_count': '5',
            'view_id': '202507192327045A44B8384571BC6D975F',
            'timestamp': '1752938824669'
        }
        
        # 使用真实的 a_bogus 和 msToken
        real_params = {
            'a_bogus': 'Y6UQXc2aMsm1r8leBXkz9SaovZm0YW5IgZENcvvqGtw6',
            'msToken': 'i-xsk6c9NdMyESYoCFII6rDNcL0WJWj8H2Dd5kWdFHxRjaof9SRaenwUUDx1tpNhvnDCY8AwuC8JYiUhJlIjC33hagPi0vit5l_xYgeonvTLyrbPvyHzQzaIKm5DO48='
        }
        
        test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
        
        try:
            response = self.session.post(
                "https://www.baike.com/api/v2/search/getDocSugDataV2",
                params=real_params,
                json=test_data,
                cookies=cookies,
                timeout=10
            )
            
            print(f"📊 真实参数测试结果:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应大小: {len(response.content)} 字节")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.content:
                try:
                    json_data = response.json()
                    print(f"   JSON 响应: {json.dumps(json_data, ensure_ascii=False)[:200]}...")
                    return json_data
                except:
                    print(f"   文本响应: {response.text[:200]}...")
                    return response.text
            else:
                print("   ⚠️ 响应为空")
                return None
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def reverse_engineer_abogus(self, real_abogus):
        """尝试逆向工程 a_bogus 的生成逻辑"""
        print(f"\n🔧 逆向分析 a_bogus: {real_abogus}")
        
        # 尝试不同的解码方式
        decoded_attempts = []
        
        # 1. 直接 Base64 解码
        try:
            padded = real_abogus + '=' * (4 - len(real_abogus) % 4) if len(real_abogus) % 4 else real_abogus
            decoded = base64.b64decode(padded)
            decoded_attempts.append(('Base64', decoded.hex(), decoded))
            print(f"   Base64 解码: {decoded.hex()}")
        except Exception as e:
            print(f"   Base64 解码失败: {e}")
        
        # 2. URL 安全 Base64 解码
        try:
            url_safe = real_abogus.replace('-', '+').replace('_', '/')
            padded = url_safe + '=' * (4 - len(url_safe) % 4) if len(url_safe) % 4 else url_safe
            decoded = base64.b64decode(padded)
            decoded_attempts.append(('URL-Safe Base64', decoded.hex(), decoded))
            print(f"   URL-Safe Base64 解码: {decoded.hex()}")
        except Exception as e:
            print(f"   URL-Safe Base64 解码失败: {e}")
        
        # 3. 分析字节模式
        for name, hex_str, raw_bytes in decoded_attempts:
            print(f"\n   {name} 字节分析:")
            print(f"     十六进制: {hex_str}")
            print(f"     长度: {len(raw_bytes)} 字节")
            
            # 检查是否包含时间戳
            if len(raw_bytes) >= 4:
                # 尝试解析为时间戳（大端和小端）
                import struct
                try:
                    timestamp_be = struct.unpack('>I', raw_bytes[:4])[0]
                    timestamp_le = struct.unpack('<I', raw_bytes[:4])[0]
                    print(f"     可能的时间戳 (大端): {timestamp_be} ({time.ctime(timestamp_be) if timestamp_be < 2**31 else 'Invalid'})")
                    print(f"     可能的时间戳 (小端): {timestamp_le} ({time.ctime(timestamp_le) if timestamp_le < 2**31 else 'Invalid'})")
                except:
                    pass
            
            # 检查是否包含哈希特征
            if len(raw_bytes) == 16:
                print(f"     可能是 MD5 哈希")
            elif len(raw_bytes) == 20:
                print(f"     可能是 SHA1 哈希")
            elif len(raw_bytes) == 32:
                print(f"     可能是 SHA256 哈希")
        
        return decoded_attempts
    
    def generate_improved_abogus(self, url, data):
        """基于分析结果生成改进的 a_bogus"""
        print(f"\n🔄 生成改进的 a_bogus...")
        
        # 基于真实样本的特征
        timestamp = int(time.time() * 1000)
        
        # 方法 1: 模拟真实格式 (44字符长度)
        method1_data = f"{url}|{json.dumps(data, separators=(',', ':'))}|{timestamp}"
        method1_hash = hashlib.sha256(method1_data.encode()).digest()[:32]  # 取前32字节
        method1_b64 = base64.b64encode(method1_hash).decode().rstrip('=')
        
        # 方法 2: URL 安全格式
        method2_hash = hashlib.md5(method1_data.encode()).digest()
        method2_b64 = base64.urlsafe_b64encode(method2_hash).decode().rstrip('=')
        
        # 方法 3: 混合编码
        method3_data = f"{timestamp}{url}{json.dumps(data)}"
        method3_hash = hashlib.sha1(method3_data.encode()).hexdigest()[:32]
        method3_b64 = base64.b64encode(method3_hash.encode()).decode()[:44]
        
        results = {
            'method1_sha256_b64': method1_b64,
            'method2_md5_urlsafe': method2_b64,
            'method3_sha1_mixed': method3_b64,
            'timestamp': timestamp
        }
        
        print(f"🎲 改进的生成结果:")
        for method, result in results.items():
            if isinstance(result, str):
                print(f"   {method}: {result} (长度: {len(result)})")
            else:
                print(f"   {method}: {result}")
        
        return results

def main():
    """主函数"""
    print("🔍 真实请求分析器")
    print("=" * 50)
    
    analyzer = RealRequestAnalyzer()
    
    # 1. 分析真实的 a_bogus 参数
    real_analysis = analyzer.analyze_real_abogus_from_curl()
    
    # 2. 测试真实参数的请求
    real_response = analyzer.test_with_real_cookies()
    
    # 3. 逆向分析 a_bogus
    real_abogus = "Y6UQXc2aMsm1r8leBXkz9SaovZm0YW5IgZENcvvqGtw6"
    decoded_results = analyzer.reverse_engineer_abogus(real_abogus)
    
    # 4. 生成改进的 a_bogus
    test_url = "https://www.baike.com/api/v2/search/getDocSugDataV2"
    test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
    improved_results = analyzer.generate_improved_abogus(test_url, test_data)
    
    # 5. 保存分析结果
    # 转换 set 为 list 以便 JSON 序列化
    real_analysis_copy = real_analysis.copy()
    real_analysis_copy['charset'] = list(real_analysis['charset'])

    analysis_data = {
        'timestamp': time.time(),
        'real_abogus': real_abogus,
        'real_analysis': real_analysis_copy,
        'decoded_results': [(name, hex_str) for name, hex_str, _ in decoded_results],
        'improved_results': improved_results,
        'real_response_success': real_response is not None
    }
    
    with open('real_request_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 分析结果已保存到: real_request_analysis.json")
    
    if real_response:
        print("✅ 真实参数请求成功，说明参数格式正确")
        print("💡 建议：基于解码结果调整生成算法")
    else:
        print("❌ 真实参数请求也失败，可能需要更多参数")
        print("💡 建议：检查 Cookie、请求头等其他因素")

if __name__ == "__main__":
    main()
