import { BaikeSearcher } from '../src/searcher.js';
import fs from 'fs';
import path from 'path';

// 高级用法示例：结果导出
async function exportResultsExample() {
  console.log('📤 结果导出示例\n');
  
  const searcher = new BaikeSearcher({ headless: true });

  try {
    const keywords = ['王小亿', '演员', '中国短剧演员'];
    const allResults = {};
    
    for (const keyword of keywords) {
      console.log(`🔍 搜索: ${keyword}`);
      const results = await searcher.search(keyword);
      allResults[keyword] = results;
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 导出为JSON文件
    const outputPath = path.join(process.cwd(), 'search-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
    
    console.log(`✅ 结果已导出到: ${outputPath}`);
    
    // 生成简单的报告
    console.log('\n📊 搜索报告:');
    let totalResults = 0;
    for (const [keyword, results] of Object.entries(allResults)) {
      console.log(`   ${keyword}: ${results.length} 个结果`);
      totalResults += results.length;
    }
    console.log(`   总计: ${totalResults} 个结果`);
    
  } catch (error) {
    console.error('❌ 导出失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 高级用法示例：结果过滤
async function filterResultsExample() {
  console.log('\n🔍 结果过滤示例\n');
  
  const searcher = new BaikeSearcher({ headless: true });

  try {
    const results = await searcher.search('演员');
    console.log(`📋 原始结果数: ${results.length}`);
    
    // 过滤包含特定关键词的结果
    const filteredResults = results.filter(result => 
      result.Title.includes('王') || result.Subtitle.includes('演员')
    );
    
    console.log(`🎯 过滤后结果数: ${filteredResults.length}`);
    
    if (filteredResults.length > 0) {
      console.log('\n📋 过滤后的结果:');
      filteredResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.Title} - ${result.Subtitle}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 过滤示例失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 高级用法示例：重试机制
async function retryExample() {
  console.log('\n🔄 重试机制示例\n');
  
  const searcher = new BaikeSearcher({ headless: true });

  async function searchWithRetry(keyword, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 尝试 ${attempt}/${maxRetries}: 搜索 "${keyword}"`);
        const results = await searcher.search(keyword);
        console.log(`✅ 成功! 找到 ${results.length} 个结果`);
        return results;
        
      } catch (error) {
        console.log(`❌ 尝试 ${attempt} 失败: ${error.message}`);
        
        if (attempt === maxRetries) {
          throw new Error(`所有 ${maxRetries} 次尝试都失败了`);
        }
        
        // 等待后重试
        const delay = attempt * 2000; // 递增延迟
        console.log(`⏳ 等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  try {
    const results = await searchWithRetry('王小亿');
    console.log(`🎉 最终结果: ${results.length} 个`);
    
  } catch (error) {
    console.error('❌ 重试示例失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 高级用法示例：性能监控
async function performanceMonitoringExample() {
  console.log('\n⚡ 性能监控示例\n');
  
  const searcher = new BaikeSearcher({ headless: true });

  try {
    const keywords = ['王小亿', '演员', '中国'];
    const performanceData = [];
    
    for (const keyword of keywords) {
      const startTime = Date.now();
      const startMemory = process.memoryUsage();
      
      console.log(`🔍 搜索: ${keyword}`);
      const results = await searcher.search(keyword);
      
      const endTime = Date.now();
      const endMemory = process.memoryUsage();
      
      const performance = {
        keyword,
        duration: endTime - startTime,
        resultsCount: results.length,
        memoryUsed: endMemory.heapUsed - startMemory.heapUsed,
        timestamp: new Date().toISOString()
      };
      
      performanceData.push(performance);
      
      console.log(`   ⏱️  耗时: ${performance.duration}ms`);
      console.log(`   📊 结果数: ${performance.resultsCount}`);
      console.log(`   💾 内存增量: ${(performance.memoryUsed / 1024 / 1024).toFixed(2)}MB`);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 性能总结
    console.log('\n📈 性能总结:');
    const avgDuration = performanceData.reduce((sum, p) => sum + p.duration, 0) / performanceData.length;
    const totalResults = performanceData.reduce((sum, p) => sum + p.resultsCount, 0);
    
    console.log(`   平均耗时: ${avgDuration.toFixed(0)}ms`);
    console.log(`   总结果数: ${totalResults}`);
    console.log(`   平均每秒结果数: ${(totalResults / (avgDuration / 1000)).toFixed(1)}`);
    
  } catch (error) {
    console.error('❌ 性能监控失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 运行所有高级示例
async function runAdvancedExamples() {
  try {
    await exportResultsExample();
    await filterResultsExample();
    await retryExample();
    await performanceMonitoringExample();
    
    console.log('\n🎉 所有高级示例运行完成!');
    
  } catch (error) {
    console.error('❌ 高级示例运行失败:', error.message);
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runAdvancedExamples().catch(console.error);
}
