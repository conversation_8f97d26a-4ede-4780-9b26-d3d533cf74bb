"""
52kanduanju.com 爬虫工具函数
"""

import requests
import time
import logging
import re
from typing import Optional, List, Dict, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import os

from config import HEADERS, REQUEST_DELAY, MAX_RETRIES, TIMEOUT, BASE_URL
from models import Drama, Actor, Category


def setup_logging(log_file: str, log_level: str = 'INFO') -> logging.Logger:
    """设置日志"""
    logger = logging.getLogger('52kanduanju_spider')
    logger.setLevel(getattr(logging, log_level))
    
    # 创建文件处理器
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, log_level))
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level))
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def make_request(url: str, session: Optional[requests.Session] = None) -> Optional[requests.Response]:
    """发送HTTP请求"""
    logger = logging.getLogger('52kanduanju_spider')

    if session is None:
        session = requests.Session()

    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"请求URL: {url} (尝试 {attempt + 1}/{MAX_RETRIES})")

            response = session.get(url, headers=HEADERS, timeout=TIMEOUT)
            response.raise_for_status()

            # 设置正确的编码
            response.encoding = 'utf-8'

            # 请求间隔
            time.sleep(REQUEST_DELAY)

            return response
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"请求失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                logger.error(f"请求最终失败: {url}")
                return None
    
    return None


def parse_drama_from_list_item(li_element, base_url: str = BASE_URL) -> Optional[Drama]:
    """从列表项元素解析短剧信息"""
    try:
        # 获取链接元素
        link_element = li_element.find('a')
        if not link_element:
            return None

        # 提取URL和ID
        detail_url = urljoin(base_url, link_element.get('href', ''))
        drama_id = extract_drama_id_from_url(detail_url)
        if not drama_id:
            return None

        # 提取标题
        title_element = link_element.find('strong')
        title = title_element.get_text(strip=True) if title_element else ""

        # 处理编码问题
        if title:
            try:
                # 尝试修复可能的编码问题
                title = title.encode('latin1').decode('utf-8')
            except (UnicodeDecodeError, UnicodeEncodeError):
                # 如果编码转换失败，保持原样
                pass
        
        # 提取封面图
        img_element = link_element.find('img')
        cover_url = urljoin(base_url, img_element.get('src', '')) if img_element else None
        
        # 检查是否为热门
        is_hot = bool(link_element.find(text=lambda text: text and '🔥' in text))
        
        # 提取集数和公司信息
        episodes = None
        company = None
        actors = []

        paragraphs = link_element.find_all('p')
        for p in paragraphs:
            text = p.get_text(strip=True)

            # 提取集数
            if '集数:' in text:
                episodes_match = re.search(r'集数:\s*(\d+)', text)
                if episodes_match:
                    episodes = int(episodes_match.group(1))

            # 提取公司
            if '公司:' in text:
                company_match = re.search(r'公司:\s*(.+?)(?:\s|$)', text)
                if company_match:
                    company = company_match.group(1).strip()

            # 提取演员信息
            if '演员:' in text:
                actor_match = re.search(r'演员:\s*(.+)', text)
                if actor_match:
                    actor_names = actor_match.group(1).strip()
                    if actor_names:
                        actors = [name.strip() for name in actor_names.split('、') if name.strip()]
        
        return Drama(
            drama_id=drama_id,
            title=title,
            episodes=episodes,
            company=company,
            actors=actors,
            cover_url=cover_url,
            detail_url=detail_url,
            is_hot=is_hot
        )
        
    except Exception as e:
        logger = logging.getLogger('52kanduanju_spider')
        logger.error(f"解析短剧列表项失败: {e}")
        return None


def parse_drama_detail_page(html_content: str, drama: Drama) -> Drama:
    """解析短剧详情页面"""
    logger = logging.getLogger('52kanduanju_spider')
    
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取简介
        description_p = soup.find('p', string=lambda text: text and len(text) > 50)
        if description_p:
            drama.description = description_p.get_text(strip=True)
        
        # 提取演员链接信息
        actor_links = soup.find_all('a', href=lambda href: href and '/actor/' in href)
        actors_with_ids = []
        
        for link in actor_links:
            actor_name = link.get_text(strip=True)
            actor_url = urljoin(BASE_URL, link.get('href', ''))
            actor_id = extract_actor_id_from_url(actor_url)
            
            if actor_name:
                actors_with_ids.append(actor_name)
                # 这里可以收集演员详细信息用于后续处理
        
        if actors_with_ids:
            drama.actors = actors_with_ids
        
        # 提取分类信息
        category_text_element = soup.find(text=lambda text: text and '分类：' in text)
        if category_text_element:
            category_text = category_text_element.strip()
            if '分类：' in category_text:
                categories_str = category_text.split('分类：')[1].strip()
                if categories_str:
                    drama.categories = [cat.strip() for cat in categories_str.split('、') if cat.strip()]
        
        return drama
        
    except Exception as e:
        logger.error(f"解析短剧详情页失败: {e}")
        return drama


def extract_drama_id_from_url(url: str) -> Optional[str]:
    """从URL中提取短剧ID"""
    try:
        # URL格式: https://52kanduanju.com/detail/NzQ4ODIyMzA4Njk1MzY0NzEyOQ==
        if '/detail/' in url:
            return url.split('/detail/')[-1]
        return None
    except Exception:
        return None


def extract_actor_id_from_url(url: str) -> Optional[str]:
    """从URL中提取演员ID"""
    try:
        # URL格式: https://52kanduanju.com/actor/cf9bc4105861a8377c617b87dbb61af0
        if '/actor/' in url:
            return url.split('/actor/')[-1]
        return None
    except Exception:
        return None


def parse_drama_list_from_html(html_content: str) -> List[Drama]:
    """从HTML内容解析短剧列表"""
    logger = logging.getLogger('52kanduanju_spider')
    dramas = []
    
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找短剧列表
        drama_list = soup.find('ul') or soup.find('ol') or soup.find('div', class_=lambda x: x and 'list' in x.lower())
        
        if drama_list:
            list_items = drama_list.find_all('li')
            logger.info(f"找到 {len(list_items)} 个短剧列表项")
            
            for li in list_items:
                drama = parse_drama_from_list_item(li)
                if drama:
                    dramas.append(drama)
        else:
            logger.warning("未找到短剧列表容器")
    
    except Exception as e:
        logger.error(f"解析短剧列表失败: {e}")
    
    return dramas
