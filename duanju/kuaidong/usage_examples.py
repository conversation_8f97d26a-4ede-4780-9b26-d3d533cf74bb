#!/usr/bin/env python3
"""
批量上传处理器使用示例
演示各种常见的使用场景
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    # 询问用户是否执行
    response = input("是否执行此命令? (y/n): ").strip().lower()
    if response != 'y':
        print("跳过此示例")
        return
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except Exception as e:
        print(f"执行失败: {e}")

def main():
    """主函数"""
    print("批量上传处理器使用示例")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("run_batch_upload.py"):
        print("错误: 请在包含 run_batch_upload.py 的目录中运行此脚本")
        sys.exit(1)
    
    examples = [
        {
            "cmd": "python test_environment.py",
            "desc": "检查环境配置"
        },
        {
            "cmd": "python run_batch_upload.py --list-actors",
            "desc": "列出所有演员信息"
        },
        {
            "cmd": "python run_batch_upload.py --kuaidong-id 7387251624085438474",
            "desc": "处理指定演员的第一个视频"
        },
        {
            "cmd": "python run_batch_upload.py --kuaidong-id 7387251624085438474 --all-videos",
            "desc": "处理指定演员的所有视频"
        },
        {
            "cmd": "python run_batch_upload.py --actor-id 33",
            "desc": "按演员ID处理"
        },
        {
            "cmd": "python run_batch_upload.py --test",
            "desc": "测试模式（处理前3个有视频的演员）"
        },
        {
            "cmd": "python run_batch_upload.py --start 0 --end 5",
            "desc": "处理索引0-4的演员"
        },
        {
            "cmd": "python run_batch_upload.py --all-videos",
            "desc": "处理所有有视频的演员的所有视频"
        },
        {
            "cmd": "python run_batch_upload.py --include-no-videos",
            "desc": "包括没有视频的演员（会跳过但记录）"
        }
    ]
    
    print("\n可用的示例命令:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['desc']}")
    
    print("\n选择要执行的示例 (输入数字，0退出，a执行所有):")
    
    while True:
        choice = input("请选择: ").strip()
        
        if choice == '0':
            print("退出")
            break
        elif choice == 'a':
            print("执行所有示例...")
            for example in examples:
                run_command(example['cmd'], example['desc'])
            break
        elif choice.isdigit():
            idx = int(choice) - 1
            if 0 <= idx < len(examples):
                example = examples[idx]
                run_command(example['cmd'], example['desc'])
            else:
                print("无效的选择")
        else:
            print("请输入有效的数字")

if __name__ == "__main__":
    main()
