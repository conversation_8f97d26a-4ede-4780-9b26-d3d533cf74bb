#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的提取报告
分析提取结果并生成可读性强的报告

作者: AI Assistant
创建时间: 2025-07-19
"""

import json
from typing import Dict, List, Optional, Any

def load_extraction_results(file_path: str) -> List[Dict]:
    """加载提取结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_summary_report(results: List[Dict]) -> Dict:
    """生成汇总报告"""
    total = len(results)
    
    # 统计各类信息的提取情况
    stats = {
        "总处理演员数": total,
        "小红书ID提取成功": sum(1 for r in results if r.get('xiaohongshu_id')),
        "微博ID提取成功": sum(1 for r in results if r.get('weibo_id')),
        "微博超话ID提取成功": sum(1 for r in results if r.get('weibo_super_topic_id')),
        "演员简介提取成功": sum(1 for r in results if r.get('description')),
        "抖音ID提取成功": sum(1 for r in results if r.get('douyin_id'))
    }
    
    # 计算成功率
    success_rates = {}
    for key, value in stats.items():
        if key != "总处理演员数":
            success_rates[f"{key}_成功率"] = f"{value/total*100:.1f}%"
    
    return {**stats, **success_rates}

def generate_detailed_list(results: List[Dict]) -> List[Dict]:
    """生成详细列表"""
    detailed_list = []
    
    for result in results:
        item = {
            "演员姓名": result.get('actor_name', ''),
            "演员ID": result.get('actor_id', ''),
            "小红书ID": result.get('xiaohongshu_id', '未找到'),
            "微博ID": result.get('weibo_id', '未找到'),
            "微博超话ID": result.get('weibo_super_topic_id', '未找到'),
            "抖音ID": result.get('douyin_id', '未找到'),
            "演员简介": result.get('description', '未找到')[:100] + "..." if result.get('description') and len(result.get('description', '')) > 100 else result.get('description', '未找到'),
            "LLM分析": "是" if result.get('llm_analysis') else "否"
        }
        detailed_list.append(item)
    
    return detailed_list

def generate_successful_extractions(results: List[Dict]) -> Dict:
    """生成成功提取的案例"""
    successful_cases = {
        "小红书ID成功案例": [],
        "微博ID成功案例": [],
        "微博超话ID成功案例": [],
        "抖音ID成功案例": []
    }
    
    for result in results:
        actor_name = result.get('actor_name', '')
        
        if result.get('xiaohongshu_id'):
            successful_cases["小红书ID成功案例"].append({
                "演员": actor_name,
                "小红书ID": result['xiaohongshu_id']
            })
        
        if result.get('weibo_id'):
            successful_cases["微博ID成功案例"].append({
                "演员": actor_name,
                "微博ID": result['weibo_id']
            })
        
        if result.get('weibo_super_topic_id'):
            successful_cases["微博超话ID成功案例"].append({
                "演员": actor_name,
                "微博超话ID": result['weibo_super_topic_id']
            })
        
        if result.get('douyin_id'):
            successful_cases["抖音ID成功案例"].append({
                "演员": actor_name,
                "抖音ID": result['douyin_id']
            })
    
    return successful_cases

def save_to_csv(results: List[Dict], output_file: str):
    """保存到CSV文件"""
    detailed_list = generate_detailed_list(results)

    # 写入CSV文件
    import csv
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        if detailed_list:
            fieldnames = detailed_list[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(detailed_list)

def main():
    """主函数"""
    # 加载提取结果
    results = load_extraction_results("operations_log/extracted_social_info_v2.json")
    
    # 生成汇总报告
    summary = generate_summary_report(results)
    print("=== 提取汇总报告 ===")
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    # 生成详细报告
    detailed_report = {
        "提取汇总": summary,
        "详细列表": generate_detailed_list(results),
        "成功案例": generate_successful_extractions(results)
    }
    
    # 保存详细报告
    with open("operations_log/detailed_extraction_report.json", 'w', encoding='utf-8') as f:
        json.dump(detailed_report, f, ensure_ascii=False, indent=2)
    
    # 保存到Excel
    try:
        save_to_excel(results, "operations_log/演员社交媒体信息提取报告.xlsx")
        print("\nExcel报告已保存到: operations_log/演员社交媒体信息提取报告.xlsx")
    except ImportError:
        print("\n注意: 需要安装pandas和openpyxl才能生成Excel报告")
        print("运行: pip install pandas openpyxl")
    
    print("\n详细报告已保存到: operations_log/detailed_extraction_report.json")
    
    # 显示一些成功案例
    successful = generate_successful_extractions(results)
    print("\n=== 成功提取案例展示 ===")
    
    for category, cases in successful.items():
        if cases:
            print(f"\n{category} (共{len(cases)}个):")
            for i, case in enumerate(cases[:3]):  # 只显示前3个
                print(f"  {i+1}. {case}")
            if len(cases) > 3:
                print(f"  ... 还有{len(cases)-3}个案例")

if __name__ == "__main__":
    main()
