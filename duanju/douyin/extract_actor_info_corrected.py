#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员信息提取脚本（修正版）
根据用户指出的具体规则进行精确提取
"""

import json
import os
import re
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Optional

def load_target_actors() -> Dict[str, str]:
    """加载目标演员列表"""
    try:
        with open("duanju_actors_need_scrape.json", "r", encoding="utf-8") as f:
            actors = json.load(f)
            return {str(actor["id"]): actor["name"] for actor in actors}
    except Exception as e:
        print(f"读取目标演员列表失败: {e}")
        return {}

def load_search_results() -> Dict[str, Any]:
    """加载搜索结果"""
    try:
        with open("search_user_results.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取搜索结果失败: {e}")
        return {}

def extract_weibo_id_from_url(url: str) -> Optional[str]:
    """从微博URL中提取微博ID"""
    # 示例: https://m.weibo.cn/n/%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF
    if "m.weibo.cn/n/" in url:
        # 提取URL编码的部分
        match = re.search(r'm\.weibo\.cn/n/([^/?&]+)', url)
        if match:
            encoded_id = match.group(1)
            try:
                # URL解码
                decoded_id = urllib.parse.unquote(encoded_id)
                return decoded_id
            except Exception:
                return encoded_id
    
    # 其他微博URL格式
    if "weibo.com/u/" in url:
        match = re.search(r'weibo\.com/u/(\d+)', url)
        if match:
            return match.group(1)
    
    if "weibo.com/" in url and "/u/" not in url:
        match = re.search(r'weibo\.com/([a-zA-Z0-9_]+)', url)
        if match:
            return match.group(1)
    
    return None

def extract_weibo_super_topic_id(url: str) -> Optional[str]:
    """从微博超话URL中提取containerid"""
    # 示例: https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF&containerid=1008083a3b0dce4bca7313f49dbebb5f983cae&luicode=10000011&lfid=1005057812743270&featurecode=newtitle
    if "m.weibo.cn/p/index" in url and "containerid=" in url:
        match = re.search(r'containerid=([a-zA-Z0-9]+)', url)
        if match:
            return match.group(1)
    
    return None

def extract_douyin_id_from_url(url: str) -> Optional[str]:
    """从抖音URL中提取用户ID"""
    # 示例: https://www.douyin.com/user/
    if "douyin.com/user/" in url:
        match = re.search(r'douyin\.com/user/([a-zA-Z0-9_]+)', url)
        if match:
            return match.group(1)
    
    return None

def extract_bio_from_metadata(item: Dict) -> str:
    """从metadata中提取简介"""
    bio_parts = []
    
    # 检查metadata中的各种描述字段
    metadata = item.get("metadata", {})
    if metadata:
        # 优先检查ogDescription
        og_description = metadata.get("ogDescription", "")
        if og_description and len(og_description) > 20:
            bio_parts.append(og_description)
        
        # 检查description
        description = metadata.get("description", "")
        if description and len(description) > 20 and description != og_description:
            bio_parts.append(description)
        
        # 检查其他可能的描述字段
        for key in ["summary", "abstract", "content"]:
            value = metadata.get(key, "")
            if value and len(value) > 20 and value not in bio_parts:
                bio_parts.append(value)
    
    # 如果metadata中没有找到，检查其他字段
    if not bio_parts:
        description = item.get("description", "")
        if description and len(description) > 20:
            bio_parts.append(description)
    
    return " ".join(bio_parts) if bio_parts else ""

def is_baike_or_wiki_source(url: str) -> bool:
    """判断是否是百科类网站"""
    baike_domains = [
        "baike.baidu.com",
        "zh.wikipedia.org",
        "baike.sogou.com",
        "baike.so.com",
        "hudong.com"
    ]
    return any(domain in url for domain in baike_domains)

def extract_actor_info(actor_name: str, search_data: List[Dict]) -> Dict[str, Any]:
    """提取单个演员的信息"""
    result = {
        "xiaohongshu_id": None,
        "weibo_id": None,
        "weibo_super_topic_id": None,
        "douyin_id": None,
        "actor_bio": "",
        "sources": []
    }
    
    bio_parts = []
    seen_bios = set()
    
    for item in search_data:
        url = item.get("url", "")
        
        # 提取微博ID
        if "weibo.cn" in url or "weibo.com" in url:
            if not result["weibo_id"]:
                weibo_id = extract_weibo_id_from_url(url)
                if weibo_id:
                    result["weibo_id"] = weibo_id
            
            # 提取微博超话ID
            if not result["weibo_super_topic_id"]:
                super_topic_id = extract_weibo_super_topic_id(url)
                if super_topic_id:
                    result["weibo_super_topic_id"] = super_topic_id
        
        # 提取抖音ID
        if "douyin.com" in url and not result["douyin_id"]:
            douyin_id = extract_douyin_id_from_url(url)
            if douyin_id:
                result["douyin_id"] = douyin_id
        
        # 提取小红书ID（如果有相关URL）
        if ("xiaohongshu.com" in url or "xhs.com" in url) and not result["xiaohongshu_id"]:
            # 小红书用户ID提取逻辑
            if "user/" in url:
                match = re.search(r'user/([a-zA-Z0-9]+)', url)
                if match:
                    result["xiaohongshu_id"] = match.group(1)
        
        # 提取简介（优先从百科类网站）
        if is_baike_or_wiki_source(url):
            bio = extract_bio_from_metadata(item)
            if bio and bio not in seen_bios:
                bio_parts.insert(0, bio)  # 百科类信息优先
                seen_bios.add(bio)
        else:
            bio = extract_bio_from_metadata(item)
            if bio and bio not in seen_bios:
                bio_parts.append(bio)
                seen_bios.add(bio)
        
        # 记录信息来源
        if url:
            result["sources"].append(url)
    
    # 合并简介，去重
    if bio_parts:
        # 取前3个最有价值的简介
        result["actor_bio"] = " ".join(bio_parts[:3])
    
    # 去重sources
    result["sources"] = list(set(result["sources"]))
    
    return result

def process_actors():
    """处理所有演员信息"""
    target_actors = load_target_actors()
    search_results = load_search_results()
    
    if not target_actors:
        print("没有找到目标演员列表")
        return
    
    if not search_results:
        print("没有找到搜索结果")
        return
    
    extracted_info = {}
    processed_count = 0
    
    print(f"开始处理 {len(target_actors)} 个演员的信息...")
    print("=" * 60)
    
    for actor_id, actor_name in target_actors.items():
        if actor_id in search_results:
            search_data = search_results[actor_id]
            
            if search_data.get("success") and search_data.get("data"):
                print(f"处理演员: {actor_name} (ID: {actor_id})")
                
                # 提取信息
                info = extract_actor_info(actor_name, search_data["data"])
                
                extracted_info[actor_id] = {
                    "name": actor_name,
                    "xiaohongshu_id": info["xiaohongshu_id"],
                    "weibo_id": info["weibo_id"],
                    "weibo_super_topic_id": info["weibo_super_topic_id"],
                    "douyin_id": info["douyin_id"],
                    "actor_bio": info["actor_bio"],
                    "sources": info["sources"],
                    "extracted_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                # 显示提取结果
                found_items = []
                if info["xiaohongshu_id"]:
                    found_items.append(f"小红书: {info['xiaohongshu_id']}")
                if info["weibo_id"]:
                    found_items.append(f"微博: {info['weibo_id']}")
                if info["weibo_super_topic_id"]:
                    found_items.append(f"微博超话: {info['weibo_super_topic_id']}")
                if info["douyin_id"]:
                    found_items.append(f"抖音: {info['douyin_id']}")
                
                if found_items:
                    print(f"  ✅ 提取到: {', '.join(found_items)}")
                else:
                    print(f"  ❌ 未提取到社交媒体账号")
                
                if info["actor_bio"]:
                    print(f"  📝 简介: {info['actor_bio'][:100]}...")
                
                processed_count += 1
                print("-" * 60)
                
            else:
                print(f"❌ 演员 {actor_name} (ID: {actor_id}) 的搜索结果无效")
        else:
            print(f"❌ 演员 {actor_name} (ID: {actor_id}) 没有搜索结果")
    
    # 保存结果
    output_file = "extracted_actor_info_corrected.json"
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(extracted_info, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 信息提取完成！")
        print(f"处理了 {processed_count} 个演员")
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")
        return
    
    # 创建统计报告
    create_summary_report(extracted_info)

def create_summary_report(extracted_info: Dict[str, Any]):
    """创建统计报告"""
    total_actors = len(extracted_info)
    has_xiaohongshu = sum(1 for info in extracted_info.values() if info.get("xiaohongshu_id"))
    has_weibo = sum(1 for info in extracted_info.values() if info.get("weibo_id"))
    has_weibo_topic = sum(1 for info in extracted_info.values() if info.get("weibo_super_topic_id"))
    has_douyin = sum(1 for info in extracted_info.values() if info.get("douyin_id"))
    has_bio = sum(1 for info in extracted_info.values() if info.get("actor_bio"))
    
    report = {
        "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "extraction_method": "corrected_rules_based",
        "total_actors_processed": total_actors,
        "statistics": {
            "xiaohongshu_accounts_found": has_xiaohongshu,
            "weibo_accounts_found": has_weibo,
            "weibo_super_topics_found": has_weibo_topic,
            "douyin_accounts_found": has_douyin,
            "actor_bios_extracted": has_bio
        },
        "coverage_rates": {
            "xiaohongshu_coverage": f"{(has_xiaohongshu/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_coverage": f"{(has_weibo/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_topic_coverage": f"{(has_weibo_topic/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "douyin_coverage": f"{(has_douyin/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "bio_coverage": f"{(has_bio/total_actors*100):.1f}%" if total_actors > 0 else "0%"
        },
        "extraction_rules": {
            "weibo_id_source": "从 m.weibo.cn/n/ URL中提取并URL解码",
            "weibo_super_topic_source": "从微博超话URL的containerid参数提取",
            "douyin_id_source": "从 douyin.com/user/ URL中提取",
            "bio_source": "优先从百科类网站的metadata.ogDescription提取"
        }
    }
    
    # 保存统计报告
    report_file = "extraction_corrected_report.json"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"📊 统计报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存统计报告失败: {e}")
    
    # 打印统计信息
    print(f"\n📊 修正版提取统计:")
    print(f"总处理演员数: {total_actors}")
    print(f"小红书账号: {has_xiaohongshu} ({report['coverage_rates']['xiaohongshu_coverage']})")
    print(f"微博账号: {has_weibo} ({report['coverage_rates']['weibo_coverage']})")
    print(f"微博超话: {has_weibo_topic} ({report['coverage_rates']['weibo_topic_coverage']})")
    print(f"抖音账号: {has_douyin} ({report['coverage_rates']['douyin_coverage']})")
    print(f"演员简介: {has_bio} ({report['coverage_rates']['bio_coverage']})")

if __name__ == "__main__":
    process_actors()
