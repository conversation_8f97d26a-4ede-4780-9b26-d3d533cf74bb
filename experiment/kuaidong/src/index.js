import { BaikeSearcher } from './searcher.js';
import logger from './logger.js';

async function main() {
  const searcher = new BaikeSearcher({
    headless: false, // 显示浏览器窗口用于调试
    timeout: 30000
  });

  try {
    // 测试搜索
    const keyword = '王小亿';
    console.log(`开始搜索: ${keyword}`);

    const interceptResult = await searcher.search(keyword);

    console.log('\n🎉 拦截结果:');
    console.log(JSON.stringify(interceptResult, null, 2));

    if (interceptResult.intercepted) {
      console.log('\n📋 成功提取的参数:');
      console.log(`   URL: ${interceptResult.url}`);
      console.log(`   msToken: ${interceptResult.msToken}`);
      console.log(`   a_bogus: ${interceptResult.aBogus}`);
      console.log(`   请求方法: ${interceptResult.method}`);
      if (interceptResult.postData) {
        console.log(`   POST数据: ${interceptResult.postData}`);
      }
    }

  } catch (error) {
    console.error('❌ 搜索失败:', error.message);
    logger.logError('main', error);
  } finally {
    await searcher.close();
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { BaikeSearcher };
