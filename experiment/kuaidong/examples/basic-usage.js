import { BaikeSearcher } from '../src/searcher.js';

async function basicExample() {
  console.log('📚 基础使用示例\n');
  
  // 创建搜索器实例
  const searcher = new BaikeSearcher({
    headless: true,  // 无头模式，不显示浏览器窗口
    timeout: 30000   // 30秒超时
  });

  try {
    // 搜索单个关键词
    console.log('🔍 搜索: 王小亿');
    const results = await searcher.search('王小亿');
    
    console.log(`✅ 找到 ${results.length} 个结果:`);
    
    if (results.length > 0) {
      console.log('\n📋 第一个结果:');
      console.log(JSON.stringify(results[0], null, 2));
      
      console.log('\n📋 所有结果:');
      results.forEach((result, index) => {
        console.log(`${index + 1}. ${result.Title} (${result.Subtitle})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 搜索失败:', error.message);
  } finally {
    // 关闭浏览器
    await searcher.close();
  }
}

async function batchExample() {
  console.log('\n📦 批量搜索示例\n');
  
  const searcher = new BaikeSearcher({ headless: true });

  try {
    // 批量搜索多个关键词
    const keywords = ['王小亿', '演员', '中国短剧'];
    console.log(`🔍 批量搜索: ${keywords.join(', ')}`);
    
    const results = await searcher.batchSearch(keywords);
    
    console.log('\n📊 批量搜索结果:');
    for (const [keyword, result] of Object.entries(results)) {
      if (result && result.length > 0) {
        console.log(`✅ ${keyword}: 找到 ${result.length} 个结果`);
        console.log(`   第一个: ${result[0].Title}`);
      } else {
        console.log(`❌ ${keyword}: 未找到结果`);
      }
    }
    
  } catch (error) {
    console.error('❌ 批量搜索失败:', error.message);
  } finally {
    await searcher.close();
  }
}

async function customOptionsExample() {
  console.log('\n⚙️  自定义配置示例\n');
  
  // 使用自定义配置
  const searcher = new BaikeSearcher({
    headless: false,  // 显示浏览器窗口
    timeout: 60000    // 60秒超时
  });

  try {
    console.log('🔍 使用自定义配置搜索...');
    const results = await searcher.search('中国短剧演员');
    
    console.log(`✅ 搜索完成，结果数: ${results.length}`);
    
  } catch (error) {
    console.error('❌ 搜索失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 运行所有示例
async function runAllExamples() {
  try {
    await basicExample();
    await batchExample();
    await customOptionsExample();
    
    console.log('\n🎉 所有示例运行完成!');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error.message);
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples().catch(console.error);
}
