#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实 a_bogus 分析的精确生成器
根据逆向分析的结果，实现更准确的 a_bogus 参数生成
"""

import hashlib
import hmac
import time
import random
import string
import base64
import json
import struct
from urllib.parse import urlparse

class AccurateABogusGenerator:
    """精确的 a_bogus 生成器"""
    
    def __init__(self):
        # 从真实样本分析得出的特征
        self.target_length = 44  # Base64 编码后的长度
        self.decoded_length = 33  # 解码后的字节长度
        
        # 可能的密钥和常量
        self.possible_keys = [
            "484e4f4a403f524300071629009a8af00000002a99f1a33e",
            "mhe",
            "bdms",
            "baike",
            "toutiao"
        ]
        
        # 字符集（从真实样本提取）
        self.charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    
    def _get_timestamp_bytes(self, timestamp=None):
        """获取时间戳字节（大端序）"""
        if timestamp is None:
            timestamp = int(time.time())
        return struct.pack('>I', timestamp)  # 4字节大端序
    
    def _generate_signature_data(self, url, data, timestamp):
        """生成签名数据"""
        # 构建签名字符串
        components = []
        
        # 添加 URL 路径
        parsed_url = urlparse(url)
        components.append(parsed_url.path)
        
        # 添加数据
        if data:
            if isinstance(data, dict):
                data_str = json.dumps(data, separators=(',', ':'), sort_keys=True)
            else:
                data_str = str(data)
            components.append(data_str)
        
        # 添加时间戳
        components.append(str(timestamp))
        
        return '|'.join(components)
    
    def method_timestamp_hash(self, url, data=None, timestamp=None):
        """方法1: 时间戳 + 哈希组合（基于真实样本分析）"""
        if timestamp is None:
            timestamp = int(time.time())
        
        # 生成签名数据
        sign_data = self._generate_signature_data(url, data, timestamp)
        
        # 生成哈希（29字节）
        hash_data = hashlib.sha256(sign_data.encode()).digest()[:29]
        
        # 组合：4字节时间戳 + 29字节哈希 = 33字节
        timestamp_bytes = self._get_timestamp_bytes(timestamp)
        combined = timestamp_bytes + hash_data
        
        # Base64 编码
        result = base64.b64encode(combined).decode().rstrip('=')
        
        return result
    
    def method_hmac_timestamp(self, url, data=None, timestamp=None):
        """方法2: HMAC + 时间戳"""
        if timestamp is None:
            timestamp = int(time.time())
        
        sign_data = self._generate_signature_data(url, data, timestamp)
        
        # 尝试不同的密钥
        for key in self.possible_keys:
            # HMAC-SHA256
            hmac_result = hmac.new(
                key.encode(),
                sign_data.encode(),
                hashlib.sha256
            ).digest()[:29]
            
            # 组合时间戳
            timestamp_bytes = self._get_timestamp_bytes(timestamp)
            combined = timestamp_bytes + hmac_result
            
            result = base64.b64encode(combined).decode().rstrip('=')
            
            # 检查长度是否匹配
            if len(result) == self.target_length:
                return result
        
        # 如果没有匹配的密钥，使用默认方法
        return self.method_timestamp_hash(url, data, timestamp)
    
    def method_custom_algorithm(self, url, data=None, timestamp=None):
        """方法3: 自定义算法（模拟字节跳动风格）"""
        if timestamp is None:
            timestamp = int(time.time())
        
        # 构建更复杂的签名数据
        parsed_url = urlparse(url)
        
        # 组件1: URL信息
        url_hash = hashlib.md5(f"{parsed_url.netloc}{parsed_url.path}".encode()).digest()[:8]
        
        # 组件2: 数据哈希
        if data:
            data_str = json.dumps(data, separators=(',', ':'), sort_keys=True)
            data_hash = hashlib.sha1(data_str.encode()).digest()[:8]
        else:
            data_hash = b'\x00' * 8
        
        # 组件3: 时间戳相关
        timestamp_bytes = self._get_timestamp_bytes(timestamp)
        time_hash = hashlib.md5(timestamp_bytes).digest()[:8]
        
        # 组件4: 随机盐（固定种子确保可重现）
        random.seed(timestamp)
        salt = random.randbytes(5)
        
        # 组合所有组件: 4 + 8 + 8 + 8 + 5 = 33字节
        combined = timestamp_bytes + url_hash + data_hash + time_hash + salt
        
        # Base64 编码
        result = base64.b64encode(combined).decode().rstrip('=')
        
        return result
    
    def method_reverse_engineered(self, url, data=None, timestamp=None):
        """方法4: 基于真实样本的逆向工程"""
        if timestamp is None:
            timestamp = int(time.time())
        
        # 真实样本的解码结果分析
        # 63a5105d cd9a32c9 b5afc95e 057933f5 26a8bd99 b4616e48 81910d72 fbea1adc 3a
        # 前4字节是时间戳，后面是某种哈希
        
        # 构建签名字符串（尝试匹配真实算法）
        sign_components = [
            url,
            str(timestamp * 1000),  # 毫秒时间戳
        ]
        
        if data:
            sign_components.append(json.dumps(data, separators=(',', ':')))
        
        # 添加可能的固定字符串
        sign_components.extend(['baike', 'search'])
        
        sign_str = ''.join(sign_components)
        
        # 生成哈希
        hash_result = hashlib.sha256(sign_str.encode()).digest()[:29]
        
        # 组合时间戳
        timestamp_bytes = self._get_timestamp_bytes(timestamp)
        combined = timestamp_bytes + hash_result
        
        # Base64 编码
        result = base64.b64encode(combined).decode().rstrip('=')
        
        return result
    
    def generate_mstoken(self, length=107):
        """生成 msToken（基于真实样本长度）"""
        # 真实 msToken 长度通常是 107 字符
        chars = string.ascii_letters + string.digits + '-_'
        return ''.join(random.choices(chars, k=length))
    
    def generate_all_methods(self, url, data=None):
        """生成所有方法的结果"""
        timestamp = int(time.time())
        
        results = {
            'method1_timestamp_hash': self.method_timestamp_hash(url, data, timestamp),
            'method2_hmac_timestamp': self.method_hmac_timestamp(url, data, timestamp),
            'method3_custom_algorithm': self.method_custom_algorithm(url, data, timestamp),
            'method4_reverse_engineered': self.method_reverse_engineered(url, data, timestamp),
            'mstoken': self.generate_mstoken(),
            'timestamp': timestamp
        }
        
        return results
    
    def test_generated_params(self, url, data, results):
        """测试生成的参数"""
        import requests
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Origin': 'https://www.baike.com',
            'Referer': 'https://www.baike.com/',
        })
        
        test_results = {}
        
        for method_name, abogus_value in results.items():
            if method_name in ['mstoken', 'timestamp']:
                continue
                
            print(f"🧪 测试 {method_name}...")
            print(f"   a_bogus: {abogus_value} (长度: {len(abogus_value)})")
            
            try:
                response = session.post(
                    url,
                    params={
                        'a_bogus': abogus_value,
                        'msToken': results['mstoken']
                    },
                    json=data,
                    timeout=10
                )
                
                test_results[method_name] = {
                    'status_code': response.status_code,
                    'response_size': len(response.content),
                    'success': response.status_code == 200 and len(response.content) > 0
                }
                
                if test_results[method_name]['success']:
                    print(f"   ✅ 成功！状态码: {response.status_code}, 响应大小: {len(response.content)} 字节")
                    try:
                        json_data = response.json()
                        print(f"   📊 JSON 响应: {json.dumps(json_data, ensure_ascii=False)[:100]}...")
                    except:
                        print(f"   📝 文本响应: {response.text[:100]}...")
                else:
                    print(f"   ❌ 失败。状态码: {response.status_code}, 响应大小: {len(response.content)} 字节")
                    
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
                test_results[method_name] = {'error': str(e)}
            
            print()
        
        return test_results

def main():
    """主函数"""
    print("🎯 精确 a_bogus 生成器")
    print("=" * 50)
    
    generator = AccurateABogusGenerator()
    
    # 测试参数
    test_url = "https://www.baike.com/api/v2/search/getDocSugDataV2"
    test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
    
    print(f"🌐 测试 URL: {test_url}")
    print(f"📦 测试数据: {test_data}")
    print()
    
    # 生成所有方法的结果
    results = generator.generate_all_methods(test_url, test_data)
    
    print("🔧 生成结果:")
    for method, value in results.items():
        if method == 'timestamp':
            print(f"   {method}: {value}")
        elif isinstance(value, str):
            print(f"   {method}: {value} (长度: {len(value)})")
    print()
    
    # 测试生成的参数
    test_results = generator.test_generated_params(test_url, test_data, results)
    
    # 保存结果
    output_data = {
        'timestamp': time.time(),
        'generated_results': results,
        'test_results': test_results
    }
    
    with open('accurate_abogus_results.json', 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print("💾 结果已保存到: accurate_abogus_results.json")
    
    # 分析结果
    successful_methods = [method for method, result in test_results.items() 
                         if result.get('success', False)]
    
    if successful_methods:
        print(f"\n🎉 成功的方法: {', '.join(successful_methods)}")
        print("💡 建议使用成功的方法进行后续开发")
    else:
        print("\n⚠️ 所有方法都未完全成功")
        print("💡 建议进一步分析真实请求的其他参数")

if __name__ == "__main__":
    main()
