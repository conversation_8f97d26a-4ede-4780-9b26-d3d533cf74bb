{"comparison_time": "2025-07-19 22:29:57", "extraction_methods_compared": 3, "version_statistics": {"简化版 (正则表达式)": {"total": 64, "xiaohongshu": 0, "weibo": 0, "weibo_topic": 0, "douyin": 0, "bio": 64}, "LLM增强版 (DeepSeek-V3)": {"total": 64, "xiaohongshu": 0, "weibo": 11, "weibo_topic": 7, "douyin": 9, "bio": 64}, "修正版 (精确规则)": {"total": 64, "xiaohongshu": 0, "weibo": 0, "weibo_topic": 9, "douyin": 0, "bio": 64}}, "key_findings": {"best_for_bio_extraction": "所有版本都达到100%覆盖率", "best_for_weibo_accounts": "LLM增强版 (17.2%)", "best_for_weibo_super_topics": "修正版 (14.1%)", "best_for_douyin_accounts": "LLM增强版 (14.1%)", "xiaohongshu_challenge": "所有版本都未能有效提取小红书账号"}, "successful_weibo_super_topic_cases": [{"name": "左一", "id": "130", "weibo_super_topic_id": "100808d01de2d8131c0ba97d6d1bca334ae859", "sources": ["https://baike.baidu.com/item/%E5%B7%A6%E4%B8%80/********", "https://m.weibo.cn/p/index?extparam=%E5%B7%A6%E4%B8%80%E5%90%8C%E5%AD%A6&containerid=100808d01de2d8131c0ba97d6d1bca334ae859&luicode=********&lfid=231522type%3D1%26t%3D10%26q%3D%23%E5%8D%97%E6%9E%81%E5%8D%87%E6%B8%A9%E5%90%8E%E7%9A%84%E4%BC%81%E9%B9%85%23", "https://www.bilibili.com/video/BV1rT42127pC/", "https://www.douyin.com/shipin/7306817517362628646"]}, {"name": "刘夕语", "id": "332", "weibo_super_topic_id": "1008087e31e9e75185b362155b38c1120a35ab", "sources": ["https://www.douyin.com/search/%E5%88%98%E5%A4%95%E8%AF%AD%E7%9A%84%E4%B8%AA%E4%BA%BA%E8%B5%84%E6%96%99%E7%AE%80%E4%BB%8B", "https://m.weibo.cn/p/index?extparam=%E5%88%98%E5%A4%95%E8%AF%AD&containerid=1008087e31e9e75185b362155b38c1120a35ab&luicode=********&lfid=1005057955799114"]}, {"name": "黄波", "id": "441", "weibo_super_topic_id": "100808b68058e5a404c3667a5b77e0f0b31dfd", "sources": ["https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E9%BB%84%E6%B3%A2&containerid=100808b68058e5a404c3667a5b77e0f0b31dfd&luicode=********&lfid=1005052198240835", "https://baike.baidu.com/item/%E9%BB%84%E6%B3%A2/64013772", "https://www.douyin.com/search/%E6%BC%94%E5%91%98%E9%BB%84%E6%B3%A2%E7%AE%80%E4%BB%8B", "https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E9%BB%84%E6%B3%A2&containerid=100808b68058e5a404c3667a5b77e0f0b31dfd&luicode=********&lfid=1005051059997537"]}, {"name": "王小亿", "id": "33", "weibo_super_topic_id": "1008083a3b0dce4bca7313f49dbebb5f983cae", "sources": ["https://baike.baidu.com/item/%E7%8E%8B%E5%B0%8F%E4%BA%BF/65332887", "https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF&containerid=1008083a3b0dce4bca7313f49dbebb5f983cae&luicode=********&lfid=1008083a3b0dce4bca7313f49dbebb5f983cae&featurecode=ne", "https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF&containerid=1008083a3b0dce4bca7313f49dbebb5f983cae&luicode=********&lfid=1005057812743270&featurecode=newtitle", "https://piaofang.maoyan.com/celebrity/3133298"]}, {"name": "钟熙", "id": "664", "weibo_super_topic_id": "1008085e52202e9910520ea5c207d803ec857c", "sources": ["https://www.sohu.com/a/785434383_116237", "https://www.sohu.com/a/732249804_120853270", "https://www.163.com/dy/article/JHBLUL8B0531KHEM.html", "https://www.douyin.com/shipin/7268721109275510843", "https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E9%92%9F%E7%86%99&containerid=1008085e52202e9910520ea5c207d803ec857c&luicode=********&lfid=1005057356406789&featurecode=newtitle17"]}, {"name": "张集骏", "id": "666", "weibo_super_topic_id": "1008087727eff39e3bfd8b388c15db82b3ac34", "sources": ["https://baike.baidu.com/item/%E5%BC%A0%E9%9B%86%E9%AA%8F/22132244", "https://3g.163.com/news/article/K3B0A33C0556B0OE.html", "https://m.maigoo.com/top/444739.html", "https://piaofang.maoyan.com/celebrity?id=2889554", "https://m.weibo.cn/p/index?extparam=%E5%BC%A0%E9%9B%86%E9%AA%8F&containerid=1008087727eff39e3bfd8b388c15db82b3ac34&luicode=********&lfid=1005057366446997&featurecode=newtitle%0A%E3%80%90%E6%98%93%E7%83%8A%E5%8D%83%E7%8E%BA%E3%80%91180917+%E6%97%B6%E5%B0%9A%E8%8A%AD%E8%8E%8E+%E6%98%93%E7%83%8A%E5%8D%83%E7%8E%BAcut%0A%E6%9C%80%E5%A5%BD%E7%9A%84%E6%97%B6%E5%85%89%E6%9C%89%E6%9C%80%E5%A5%BD%E7%9A%84%E4%BD%A0"]}, {"name": "郭宇欣", "id": "1570", "weibo_super_topic_id": "1008081885235a1a7b4f5de14dc704ae66e9ed", "sources": ["https://www.sohu.com/a/897568811_121373308", "https://baike.baidu.com/item/%E9%83%AD%E5%AE%87%E6%AC%A3/61555666", "https://www.sohu.com/a/897962629_121373215", "https://m.weibo.cn/p/index?extparam=%E9%83%AD%E5%AE%87%E6%AC%A3&containerid=1008081885235a1a7b4f5de14dc704ae66e9ed&luicode=********&lfid=1008082d190ea3f97eb5d79c36a6b8184bd09c&featurecode=newtitle%C2%A0"]}, {"name": "徐思祁", "id": "1431", "weibo_super_topic_id": "1008083bfa0e770e987af26da9a13cd55428aa", "sources": ["https://naodong.net/actor/1952", "https://m.weibo.cn/p/index?extparam=%E5%BE%90%E6%80%9D%E7%A5%81&containerid=1008083bfa0e770e987af26da9a13cd55428aa&luicode=20000061&lfid=5059971908371417", "https://m.maoyan.com/asgard/celebrity/2970095", "https://baike.baidu.com/item/%E5%BE%90%E6%80%9D%E7%A5%81/23674861", "https://piaofang.maoyan.com/celebrity/2970095"]}, {"name": "至春禾", "id": "3502", "weibo_super_topic_id": "100808aab0d3624cb9d096d698e88668724e06", "sources": ["https://m.bilibili.com/search?keyword=%E8%87%B3%E6%98%A5%E7%A6%BE", "https://baike.baidu.com/item/%E7%BB%BE%E5%90%9B%E5%BF%83/65768228?noadapt=1", "https://m.weibo.cn/p/index?extparam=%E8%87%B3%E6%98%A5%E7%A6%BE&containerid=100808aab0d3624cb9d096d698e88668724e06&luicode=********&lfid=231522type%3D1%26t%3D10%26q%3D%23%E5%9B%BD%E9%A3%8E%E5%A4%A7%E5%85%B8%23&featurecode=1000000", "https://m.weibo.cn/p/index?extparam=%E8%87%B3%E6%98%A5%E7%A6%BE&containerid=100808aab0d3624cb9d096d698e88668724e06&luicode=********&lfid=1008086b45ec97540dfa1de2ffd032e2d3a583&featurecode=10000087", "https://www.douyin.com/search/%E4%BB%8B%E7%BB%8D%E4%B8%80%E4%B8%8B%E8%87%B3%E6%98%A5%E7%A6%BE%E7%9A%84%E4%B8%AA%E4%BA%BA%E8%B5%84%E6%96%99"]}], "methodology_analysis": {"simple_regex": {"strengths": ["快速执行", "无需外部API", "稳定可靠"], "weaknesses": ["提取精度有限", "难以处理复杂格式", "容易遗漏信息"]}, "llm_enhanced": {"strengths": ["智能理解内容", "能处理复杂语境", "提取质量较高"], "weaknesses": ["依赖外部API", "执行时间较长", "成本较高"]}, "corrected_rules": {"strengths": ["针对性强", "提取精度高", "执行稳定"], "weaknesses": ["需要深入了解数据格式", "规则维护成本高"]}}, "recommendations": {"for_weibo_super_topics": "使用修正版的精确规则提取", "for_general_social_media": "结合LLM增强版和修正版", "for_bio_extraction": "任何版本都可以，简化版即可满足需求", "for_production_use": "建议使用修正版作为主要方法，LLM增强版作为补充"}}