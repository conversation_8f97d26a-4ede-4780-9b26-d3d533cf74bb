#!/usr/bin/env python3
"""
测试增强功能的脚本
"""

import os
import json
from batch_upload_processor import BatchUploadProcessor

def test_find_head_images():
    """测试查找头像文件功能"""
    print("=== 测试查找头像文件功能 ===")
    
    processor = BatchUploadProcessor()
    
    # 测试一个已知的kuaidong_id
    test_kuaidong_id = "7387251624085438474"
    
    head_images = processor.find_head_image_files(test_kuaidong_id)
    print(f"找到的头像文件: {len(head_images)}")
    for img in head_images:
        print(f"  - {img}")
    
    return len(head_images) > 0

def test_find_videos():
    """测试查找视频文件功能"""
    print("\n=== 测试查找视频文件功能 ===")
    
    processor = BatchUploadProcessor()
    
    # 测试一个已知的kuaidong_id
    test_kuaidong_id = "7387251624085438474"
    
    videos = processor.find_video_files(test_kuaidong_id)
    print(f"找到的视频文件: {len(videos)}")
    for video in videos:
        print(f"  - {video}")
    
    return len(videos) > 0

def test_load_actors():
    """测试加载演员数据"""
    print("\n=== 测试加载演员数据 ===")
    
    processor = BatchUploadProcessor()
    actors = processor.load_actors_data()
    
    print(f"加载的演员数量: {len(actors)}")
    
    # 显示前3个演员的信息
    for i, actor in enumerate(actors[:3]):
        print(f"演员 {i+1}:")
        print(f"  ID: {actor.get('id')}")
        print(f"  姓名: {actor.get('name')}")
        print(f"  kuaidong_id: {actor.get('kuaidong_id')}")
    
    return len(actors) > 0

def test_operations_directory():
    """测试操作记录目录创建"""
    print("\n=== 测试操作记录目录 ===")
    
    processor = BatchUploadProcessor()
    
    if os.path.exists(processor.operations_dir):
        print(f"操作记录目录已创建: {processor.operations_dir}")
        return True
    else:
        print(f"操作记录目录创建失败: {processor.operations_dir}")
        return False

def test_single_actor_processing():
    """测试单个演员处理（不实际上传）"""
    print("\n=== 测试单个演员处理逻辑 ===")
    
    processor = BatchUploadProcessor()
    actors = processor.load_actors_data()
    
    if not actors:
        print("没有演员数据可测试")
        return False
    
    # 找一个有kuaidong_id的演员
    test_actor = None
    for actor in actors:
        if actor.get('kuaidong_id'):
            test_actor = actor
            break
    
    if not test_actor:
        print("没有找到有kuaidong_id的演员")
        return False
    
    print(f"测试演员: {test_actor.get('name')} (ID: {test_actor.get('id')})")
    
    kuaidong_id = test_actor.get('kuaidong_id')
    
    # 检查文件
    head_images = processor.find_head_image_files(kuaidong_id)
    videos = processor.find_video_files(kuaidong_id)
    
    print(f"  头像文件: {len(head_images)}")
    print(f"  视频文件: {len(videos)}")
    
    if head_images:
        print("  头像文件列表:")
        for img in head_images:
            print(f"    - {os.path.basename(img)}")
    
    if videos:
        print("  视频文件列表:")
        for video in videos:
            print(f"    - {os.path.basename(video)}")
    
    return True

def main():
    """运行所有测试"""
    print("开始测试增强功能...")
    
    tests = [
        ("操作记录目录创建", test_operations_directory),
        ("加载演员数据", test_load_actors),
        ("查找头像文件", test_find_head_images),
        ("查找视频文件", test_find_videos),
        ("单个演员处理逻辑", test_single_actor_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"\n{test_name}: ✗ 异常 - {e}")
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    
    passed = 0
    for test_name, result, error in results:
        status = "✓" if result else "✗"
        print(f"{status} {test_name}")
        if error:
            print(f"    错误: {error}")
        if result:
            passed += 1
    
    print(f"\n通过: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 所有测试通过！可以开始使用增强功能。")
    else:
        print("⚠️  部分测试失败，请检查配置和文件结构。")

if __name__ == "__main__":
    main()
