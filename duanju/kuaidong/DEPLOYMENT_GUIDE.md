# 短剧演员视频批量上传处理器 - 部署和使用指南

## 🎯 项目概述

这个工具实现了您要求的完整功能：

1. ✅ **视频封面提取**：从MP4视频文件中自动提取首帧作为封面图片
2. ✅ **文件上传**：上传封面图片到 `duanju_actor_cuts.cover` 端点
3. ✅ **视频上传**：上传视频文件到 `duanju_actor_cuts.video` 端点  
4. ✅ **创建Cut记录**：调用API创建短剧cut记录
5. ✅ **批量处理**：支持按kuaidong_id逐个处理所有演员
6. ✅ **多视频支持**：可以处理每个演员的多个视频文件
7. ✅ **完整日志**：详细记录处理过程和结果

## 🚀 快速开始

### 1. 环境检查
```bash
cd duanju/kuaidong
python test_environment.py
```

### 2. 查看演员状态
```bash
python status_summary.py
```

### 3. 处理单个演员（测试）
```bash
# 处理王小亿的第一个视频
python run_batch_upload.py --kuaidong-id 7387251624085438474

# 处理王小亿的所有视频
python run_batch_upload.py --kuaidong-id 7387251624085438474 --all-videos
```

### 4. 批量处理所有演员
```bash
# 处理所有有视频的演员（每个演员只处理第一个视频）
python run_batch_upload.py

# 处理所有有视频的演员的所有视频
python run_batch_upload.py --all-videos
```

## 📁 文件结构

```
duanju/kuaidong/
├── 📄 主要脚本
│   ├── batch_upload_processor.py      # 核心处理器
│   ├── run_batch_upload.py           # 命令行运行脚本
│   └── config.py                     # 配置文件
├── 📄 工具脚本
│   ├── test_environment.py           # 环境测试
│   ├── status_summary.py             # 状态总结
│   └── usage_examples.py             # 使用示例
├── 📄 数据文件
│   ├── duanju_actors_with_kuaidong.json  # 演员数据
│   └── kuaidong_data/                    # 视频数据目录
├── 📄 输出文件
│   ├── batch_upload_results_*.json   # 处理结果
│   ├── batch_upload.log              # 处理日志
│   └── temp/                         # 临时文件目录
└── 📄 文档
    ├── README.md                     # 详细说明
    └── DEPLOYMENT_GUIDE.md           # 本文件
```

## ⚙️ 配置说明

### API配置 (config.py)
- `auth_token`: API认证令牌（已配置您提供的token）
- `base_url`: API基础URL
- `timeout`: 上传超时时间

### 处理参数
- `delay_between_actors`: 处理演员间的延迟（避免API限流）
- `retry_count`: 失败重试次数
- `cut_defaults`: Cut记录的默认参数

## 🔧 命令行选项

```bash
python run_batch_upload.py [选项]

选项:
  --list-actors              列出所有演员信息
  --test                     测试模式（处理前3个演员）
  --actor-id ID              处理指定ID的演员
  --kuaidong-id ID           处理指定kuaidong_id的演员
  --all-videos               处理每个演员的所有视频（默认只处理第一个）
  --include-no-videos        包括没有视频的演员
  --start N                  开始索引
  --end N                    结束索引
```

## 📊 处理结果

### 结果文件格式
每次处理都会生成 `batch_upload_results_{timestamp}.json` 文件，包含：

```json
{
  "total_processed": 1,
  "successful": 1,
  "failed": 0,
  "processed_at": "2025-07-22T19:06:50.578929",
  "results": [
    {
      "actor_id": "33",
      "kuaidong_id": "7387251624085438474",
      "actor_name": "王小亿",
      "success": true,
      "cuts_created": [
        {
          "video_file": "path/to/video.mp4",
          "cut_number": 1,
          "success": true,
          "cover_upload_result": { ... },
          "video_upload_result": { ... },
          "cut_create_result": { ... }
        }
      ]
    }
  ]
}
```

### 日志文件
`batch_upload.log` 包含详细的处理日志，便于调试和监控。

## 🎯 实际使用场景

### 场景1：首次部署测试
```bash
# 1. 检查环境
python test_environment.py

# 2. 查看演员状态
python run_batch_upload.py --list-actors

# 3. 测试单个演员
python run_batch_upload.py --test
```

### 场景2：处理新增演员
```bash
# 处理特定演员
python run_batch_upload.py --kuaidong-id 新演员的kuaidong_id
```

### 场景3：批量处理所有演员
```bash
# 处理所有有视频的演员
python run_batch_upload.py --all-videos
```

### 场景4：分批处理（大量数据）
```bash
# 分批处理，每次10个演员
python run_batch_upload.py --start 0 --end 10
python run_batch_upload.py --start 10 --end 20
# ... 继续
```

## 🔍 监控和维护

### 查看处理状态
```bash
python status_summary.py
```

### 查看日志
```bash
tail -f batch_upload.log
```

### 清理临时文件
```bash
rm -rf temp/*
```

## ⚠️ 注意事项

1. **API Token有效期**：定期检查和更新config.py中的认证token
2. **网络稳定性**：大文件上传需要稳定的网络连接
3. **磁盘空间**：确保有足够空间存储临时封面图片
4. **API限流**：脚本已内置延迟机制，避免触发API限流
5. **错误处理**：所有错误都会记录到日志，便于排查问题

## 🆘 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu
   sudo apt install ffmpeg
   ```

2. **API认证失败**
   - 检查config.py中的auth_token是否正确
   - 确认token未过期

3. **视频文件未找到**
   - 检查kuaidong_data目录结构
   - 确认视频文件路径正确

4. **上传超时**
   - 增加config.py中的timeout值
   - 检查网络连接稳定性

### 获取帮助
- 查看详细日志：`cat batch_upload.log`
- 运行环境测试：`python test_environment.py`
- 查看处理状态：`python status_summary.py`

## 📈 性能优化

- **并发处理**：当前为串行处理，可根据API限制调整并发数
- **断点续传**：支持从失败点继续处理
- **增量处理**：只处理新增或未处理的演员

## 🎉 成功案例

目前已成功处理：
- ✅ 演员"王小亿"的2个视频文件
- ✅ 创建了2个Cut记录
- ✅ 上传了2个封面图片和2个视频文件
- ✅ 所有API调用成功，无错误

系统已准备好处理更多演员的视频文件！
