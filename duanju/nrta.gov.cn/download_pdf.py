import os
import json
import requests
from bs4 import BeautifulSoup

# 1. 读取 duanju.json 文件内容
with open('duanju.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 2. 循环遍历 url 的值
for item in data:
    url = item['url']
    date = item['date']
    print(f'Processing: {url}')
    try:
        # 3. http 请求 url 的页面
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, 'html.parser')
        # 4. 解析网页中的 “附件 ：<a href="/module/download/downfile.jsp” a 标签部分
        a_tag = soup.find('a', href=lambda x: x and x.startswith('/module/download/downfile.jsp'))
        if not a_tag:
            print(f'No PDF link found for {url}')
            continue
        pdf_path = a_tag['href']
        pdf_url = f'https://www.nrta.gov.cn{pdf_path}'
        # 5. 下载 pdf，并命名为 {date}.pdf, 写入到 ./pdfs 目录下
        pdf_dir = './pdfs'
        os.makedirs(pdf_dir, exist_ok=True)
        pdf_file = os.path.join(pdf_dir, f'{date}.pdf')
        pdf_resp = requests.get(pdf_url, timeout=20)
        pdf_resp.raise_for_status()
        with open(pdf_file, 'wb') as f:
            f.write(pdf_resp.content)
        print(f'Downloaded: {pdf_file}')
    except Exception as e:
        print(f'Error processing {url}: {e}')
