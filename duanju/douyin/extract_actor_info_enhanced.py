#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员信息提取脚本（增强版）
结合简化版的结果和LLM智能分析，提取更准确的社交媒体账号信息
"""

import json
import os
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# 硅基流动 API 配置
SILICONFLOW_API_KEY = "sk-bntstadiguzdjgxczscotiatbawbqfgwerywdrznhjtffhix"
SILICONFLOW_API_URL = "https://api.siliconflow.cn/v1/chat/completions"
MODEL_NAME = "deepseek-ai/DeepSeek-V3"

def load_simple_results() -> Dict[str, Any]:
    """加载简化版提取的结果"""
    try:
        with open("extracted_actor_info_simple.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取简化版结果失败: {e}")
        return {}

def load_search_results() -> Dict[str, Any]:
    """加载搜索结果"""
    try:
        with open("search_user_results.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取搜索结果失败: {e}")
        return {}

def call_llm_api(prompt: str, max_retries: int = 2) -> Optional[str]:
    """调用硅基流动 LLM API"""
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的信息提取助手。请从提供的文本中提取社交媒体账号信息，只返回JSON格式的结果。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 800
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.post(SILICONFLOW_API_URL, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
                
        except Exception as e:
            print(f"API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                import time
                time.sleep(3)
    
    return None

def extract_social_media_with_llm(actor_name: str, search_data: List[Dict]) -> Dict[str, Optional[str]]:
    """使用LLM提取社交媒体账号信息"""
    
    # 准备内容摘要，只取关键信息
    content_parts = []
    for item in search_data[:3]:  # 只取前3个结果
        title = item.get("title", "")
        description = item.get("description", "")
        url = item.get("url", "")
        
        if any(keyword in url for keyword in ["xiaohongshu", "weibo", "douyin"]):
            content_parts.append(f"标题: {title}\n描述: {description}\nURL: {url}")
        elif any(keyword in title.lower() for keyword in ["小红书", "微博", "抖音"]):
            content_parts.append(f"标题: {title}\n描述: {description}")
    
    if not content_parts:
        return {"xiaohongshu_id": None, "weibo_id": None, "weibo_super_topic_id": None, "douyin_id": None}
    
    content_text = "\n\n".join(content_parts)
    
    prompt = f"""
请从以下关于演员"{actor_name}"的信息中提取社交媒体账号，返回JSON格式：

{content_text}

请提取以下信息：
{{
    "xiaohongshu_id": "小红书账号ID或用户名",
    "weibo_id": "微博账号ID或用户名", 
    "weibo_super_topic_id": "微博超话名称",
    "douyin_id": "抖音账号ID或用户名"
}}

注意：
1. 如果某项信息未找到，设置为 null
2. 只返回JSON，不要其他文字
3. 账号ID应该是具体的用户标识，不是通用描述
"""

    llm_response = call_llm_api(prompt)
    
    if llm_response:
        try:
            # 清理响应
            clean_response = llm_response.strip()
            if clean_response.startswith("```json"):
                clean_response = clean_response[7:]
            if clean_response.endswith("```"):
                clean_response = clean_response[:-3]
            clean_response = clean_response.strip()
            
            return json.loads(clean_response)
        except json.JSONDecodeError as e:
            print(f"LLM 响应解析失败: {e}")
    
    return {"xiaohongshu_id": None, "weibo_id": None, "weibo_super_topic_id": None, "douyin_id": None}

def enhance_actor_info():
    """增强演员信息"""
    simple_results = load_simple_results()
    search_results = load_search_results()
    
    if not simple_results:
        print("没有找到简化版结果")
        return
    
    enhanced_info = {}
    processed_count = 0
    
    print(f"开始增强 {len(simple_results)} 个演员的社交媒体信息...")
    
    for actor_id, actor_data in simple_results.items():
        actor_name = actor_data["name"]
        print(f"处理演员: {actor_name} (ID: {actor_id})")
        
        # 复制基本信息
        enhanced_info[actor_id] = actor_data.copy()
        
        # 如果有搜索数据，尝试用LLM提取社交媒体信息
        if actor_id in search_results:
            search_data = search_results[actor_id]
            if search_data.get("success") and search_data.get("data"):
                social_media = extract_social_media_with_llm(actor_name, search_data["data"])
                
                # 更新社交媒体信息
                enhanced_info[actor_id].update(social_media)
                
                # 显示提取结果
                found_accounts = []
                for key, value in social_media.items():
                    if value:
                        platform = key.replace("_id", "").replace("_", " ")
                        found_accounts.append(f"{platform}: {value}")
                
                if found_accounts:
                    print(f"  提取到: {', '.join(found_accounts)}")
                else:
                    print(f"  未提取到社交媒体账号")
        
        processed_count += 1
        
        # 每处理5个演员暂停一下
        if processed_count % 5 == 0:
            print(f"已处理 {processed_count} 个演员，暂停3秒...")
            import time
            time.sleep(3)
    
    # 保存增强结果
    output_file = "extracted_actor_info_enhanced.json"
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(enhanced_info, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 信息增强完成！")
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果失败: {e}")
    
    # 创建最终统计报告
    create_final_report(enhanced_info)

def create_final_report(enhanced_info: Dict[str, Any]):
    """创建最终统计报告"""
    total_actors = len(enhanced_info)
    has_xiaohongshu = sum(1 for info in enhanced_info.values() if info.get("xiaohongshu_id"))
    has_weibo = sum(1 for info in enhanced_info.values() if info.get("weibo_id"))
    has_weibo_topic = sum(1 for info in enhanced_info.values() if info.get("weibo_super_topic_id"))
    has_douyin = sum(1 for info in enhanced_info.values() if info.get("douyin_id"))
    has_bio = sum(1 for info in enhanced_info.values() if info.get("actor_bio"))
    
    report = {
        "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_actors_processed": total_actors,
        "statistics": {
            "xiaohongshu_accounts_found": has_xiaohongshu,
            "weibo_accounts_found": has_weibo,
            "weibo_super_topics_found": has_weibo_topic,
            "douyin_accounts_found": has_douyin,
            "actor_bios_extracted": has_bio
        },
        "coverage_rates": {
            "xiaohongshu_coverage": f"{(has_xiaohongshu/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_coverage": f"{(has_weibo/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_topic_coverage": f"{(has_weibo_topic/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "douyin_coverage": f"{(has_douyin/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "bio_coverage": f"{(has_bio/total_actors*100):.1f}%" if total_actors > 0 else "0%"
        },
        "improvement_over_simple": {
            "xiaohongshu_improvement": has_xiaohongshu,
            "weibo_improvement": has_weibo,
            "douyin_improvement": has_douyin
        }
    }
    
    # 保存最终报告
    report_file = "extraction_final_report.json"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"最终报告已保存到: {report_file}")
    except Exception as e:
        print(f"保存最终报告失败: {e}")
    
    # 打印统计信息
    print(f"\n📊 最终提取统计:")
    print(f"总处理演员数: {total_actors}")
    print(f"小红书账号: {has_xiaohongshu} ({report['coverage_rates']['xiaohongshu_coverage']})")
    print(f"微博账号: {has_weibo} ({report['coverage_rates']['weibo_coverage']})")
    print(f"微博超话: {has_weibo_topic} ({report['coverage_rates']['weibo_topic_coverage']})")
    print(f"抖音账号: {has_douyin} ({report['coverage_rates']['douyin_coverage']})")
    print(f"演员简介: {has_bio} ({report['coverage_rates']['bio_coverage']})")

def save_to_operations_folder():
    """将结果保存到操作记录文件夹"""
    operations_path = "../../operations"
    if os.path.exists(operations_path):
        operation_dirs = [d for d in os.listdir(operations_path) if d.startswith("douyin_actor_scrape_")]
        if operation_dirs:
            latest_operation_dir = os.path.join(operations_path, sorted(operation_dirs)[-1])
            
            # 复制结果文件到操作记录文件夹
            files_to_copy = [
                "extracted_actor_info_simple.json",
                "extracted_actor_info_enhanced.json",
                "extraction_final_report.json"
            ]
            
            for file_name in files_to_copy:
                if os.path.exists(file_name):
                    import shutil
                    dest_path = os.path.join(latest_operation_dir, file_name)
                    try:
                        shutil.copy2(file_name, dest_path)
                        print(f"已复制 {file_name} 到操作记录文件夹")
                    except Exception as e:
                        print(f"复制 {file_name} 失败: {e}")

if __name__ == "__main__":
    enhance_actor_info()
    save_to_operations_folder()
