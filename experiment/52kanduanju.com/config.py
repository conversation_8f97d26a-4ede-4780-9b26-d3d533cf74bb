"""
52kanduanju.com 爬虫配置文件
"""

# 基础配置
BASE_URL = "https://52kanduanju.com"
MAIN_PAGE_URL = f"{BASE_URL}/"

# 请求配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0'
}

# 爬虫配置
REQUEST_DELAY = 1  # 请求间隔（秒）
MAX_RETRIES = 3    # 最大重试次数
TIMEOUT = 30       # 请求超时时间（秒）

# 分类页面URL模板
CATEGORY_URL_TEMPLATE = f"{BASE_URL}/categories/{{category_slug}}"

# 已知的分类列表
CATEGORIES = [
    {"name": "全部", "slug": "all"},
    {"name": "总裁", "slug": "zong-cai"},
    {"name": "逆袭", "slug": "ni-xi"},
    {"name": "重生", "slug": "chong-sheng"},
    {"name": "女性成长", "slug": "nv-xing-cheng-zhang"},
    {"name": "现代言情", "slug": "xian-dai-yan-qing"},
    {"name": "奇幻脑洞", "slug": "qi-huan-nao-dong"},
    {"name": "无敌神医", "slug": "wu-di-shen-yi"},
    {"name": "战神归来", "slug": "zhan-shen-gui-lai"},
    {"name": "都市修仙", "slug": "du-shi-xiu-xian"},
    {"name": "闪婚", "slug": "shan-hun"},
    {"name": "马甲", "slug": "ma-jia"},
    {"name": "萌宝", "slug": "meng-bao"},
    {"name": "王妃", "slug": "wang-fei"},
    {"name": "皇后", "slug": "huang-hou"},
    {"name": "赘婿逆袭", "slug": "zhui-xu-ni-xi"},
    {"name": "历史古代", "slug": "li-shi-gu-dai"},
    {"name": "强者回归", "slug": "qiang-zhe-hui-gui"},
    {"name": "都市日常", "slug": "du-shi-ri-chang"},
    {"name": "乡村", "slug": "xiang-cun"},
    {"name": "校园", "slug": "xiao-yuan"},
    {"name": "家庭伦理", "slug": "jia-ting-lun-li"},
    {"name": "神豪", "slug": "shen-hao"},
    {"name": "职场", "slug": "zhi-chang"},
    {"name": "穿越", "slug": "chuan-yue"},
    {"name": "穿书", "slug": "chuan-shu"},
    {"name": "娱乐圈", "slug": "yu-le-quan"},
    {"name": "系统", "slug": "xi-tong"},
    {"name": "女帝", "slug": "nv-di"}
]

# 数据文件路径
DATA_DIR = "data"
DRAMAS_FILE = f"{DATA_DIR}/dramas.json"
ACTORS_FILE = f"{DATA_DIR}/actors.json"
CATEGORIES_FILE = f"{DATA_DIR}/categories.json"
LOG_FILE = f"{DATA_DIR}/spider.log"

# 日志配置
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'
