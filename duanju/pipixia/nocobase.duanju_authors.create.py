import requests
import json
import os
import time
from glob import glob

url = "https://data.wansu.tech/api/duanju_authors:create"

payload = {
  "name": "测试",
  "platform": "ppx"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Content-Type': "application/json",
  'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
  'authorization': "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NDk3MzUxMDAyMTQsImlhdCI6MTc0OTgyMTUwMCwiZXhwIjoxNzQ5OTA3OTAwLCJqdGkiOiJmNGM1MTk3YS02Y2I3LTQ1YmMtYjhiNC00ZDFiYTJmYTA1MDcifQ.0Iwht03Mp8J9VRcaOptQhxmI60IGBlijfawy0YDzmiQ",
  'origin': "https://data.wansu.tech",
  'priority': "u=1, i",
  'referer': "https://data.wansu.tech/admin/xmxjrowlwpp/popups/agmjkskci1e",
  'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?0",
  'sec-ch-ua-platform': "\"macOS\"",
  'sec-fetch-dest': "empty",
  'sec-fetch-mode': "cors",
  'sec-fetch-site': "same-origin",
  'x-authenticator': "basic",
  'x-hostname': "data.wansu.tech",
  'x-locale': "zh-CN",
  'x-role': "root",
  'x-timezone': "+08:00",
  'x-with-acl-meta': "true"
}

author_to_id = {}
bookid_to_info = {}

dir_path = '/Users/<USER>/Documents/短剧/皮皮虾/详情'
json_files = glob(os.path.join(dir_path, '*.json'))

for file_path in json_files:
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            data = json.load(f)
        except Exception as e:
            print(f'Failed to load {file_path}: {e}')
            continue
        author = data.get('data', {}).get('author')
        book_id = data.get('data', {}).get('book_id')
        if not author or not book_id:
            continue
        if author in author_to_id:
            # 已存在，直接存入 bookid_to_info
            bookid_to_info[book_id] = {"id": author_to_id[author], "name": author}
            continue
        payload = {
            "name": author,
            "platform": "ppx"
        }
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        try:
            resp_json = response.json()
            author_id = resp_json['data']['id']
            author_name = resp_json['data']['name']
            print(f'Success: author={author_name}, id={author_id}')
            author_to_id[author_name] = author_id
            bookid_to_info[book_id] = {"id": author_id, "name": author_name}
        except Exception as e:
            print(f'Failed to parse response for {author}: {e}\n{response.text}')

# 可选：输出两个 map 检查
with open('/Users/<USER>/python/daunju_authors.bookid_to_info.json', 'w', encoding='utf-8') as f:
    json.dump(bookid_to_info, f, ensure_ascii=False, indent=2)
with open('/Users/<USER>/python/daunju_authors.author_to_id.json', 'w', encoding='utf-8') as f:
    json.dump(author_to_id, f, ensure_ascii=False, indent=2)
print('bookid_to_info and author_to_id have been saved to /Users/<USER>/python/')