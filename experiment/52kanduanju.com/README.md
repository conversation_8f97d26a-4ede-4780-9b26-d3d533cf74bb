# 52kanduanju.com 短剧爬虫

这是一个用于爬取 52kanduanju.com 网站短剧数据的爬虫项目。

## 功能特性

- 爬取短剧基本信息（标题、简介、集数、公司、演员等）
- 爬取演员信息和分类数据
- 支持增量更新，避免重复爬取
- 数据保存为JSON格式
- 完善的错误处理和日志记录
- 支持请求重试和延时控制

## 项目结构

```
experiment/52kanduanju.com/
├── spider.py              # 主爬虫脚本
├── models.py              # 数据模型定义
├── utils.py               # 工具函数
├── config.py              # 配置文件
├── data/                  # 数据存储目录
│   ├── dramas.json        # 短剧数据
│   ├── actors.json        # 演员数据
│   ├── categories.json    # 分类数据
│   └── spider.log         # 爬虫日志
└── README.md              # 说明文档
```

## 数据结构

### 短剧数据 (Drama)
```json
{
  "drama_id": "NzQ4ODIyMzA4Njk1MzY0NzEyOQ==",
  "title": "被全网读心后，我成全民团宠",
  "description": "道门老祖姜瑶，本在修仙界叱咤风云...",
  "episodes": 72,
  "company": "山海星辰",
  "actors": ["王小亿", "赵海豫宁", "黄宥天"],
  "categories": ["现代言情", "穿越", "娱乐圈"],
  "cover_url": "https://52kanduanju.com/covers/NzQ4ODIyMzA4Njk1MzY0NzEyOQ==.webp",
  "detail_url": "https://52kanduanju.com/detail/NzQ4ODIyMzA4Njk1MzY0NzEyOQ==",
  "is_hot": true,
  "crawled_at": "2025-01-21T..."
}
```

### 演员数据 (Actor)
```json
{
  "name": "王小亿",
  "actor_id": "cf9bc4105861a8377c617b87dbb61af0",
  "actor_url": "https://52kanduanju.com/actor/cf9bc4105861a8377c617b87dbb61af0"
}
```

### 分类数据 (Category)
```json
{
  "name": "现代言情",
  "slug": "xian-dai-yan-qing",
  "count": 2436,
  "url": "https://52kanduanju.com/categories/xian-dai-yan-qing"
}
```

## 使用方法

### 安装依赖

```bash
pip install requests beautifulsoup4
```

### 运行爬虫

#### 完整爬取（推荐）
```bash
cd experiment/52kanduanju.com
python spider.py
```

#### 测试爬取（仅爬取5个短剧用于测试）
```bash
cd experiment/52kanduanju.com
python test_spider.py
```

### 配置说明

可以在 `config.py` 中修改以下配置：

- `REQUEST_DELAY`: 请求间隔时间（秒）
- `MAX_RETRIES`: 最大重试次数
- `TIMEOUT`: 请求超时时间
- `LOG_LEVEL`: 日志级别

## 爬取流程

1. **初始化**: 创建数据目录，设置日志，加载已有数据
2. **主页爬取**: 爬取首页的短剧列表
3. **分类爬取**: 遍历所有分类页面获取更多短剧
4. **详情爬取**: 对每个短剧爬取详情页获取完整信息
5. **数据处理**: 去重、清洗和整理数据
6. **保存数据**: 将数据保存为JSON文件

## 注意事项

- 爬虫会自动处理请求间隔，避免对服务器造成过大压力
- 支持断点续爬，重新运行时会跳过已爬取的数据
- 所有网络请求都有重试机制和超时控制
- 详细的日志记录便于调试和监控

## 输出文件

### 完整爬取输出
- `data/dramas.json`: 包含所有短剧的详细信息
- `data/actors.json`: 包含所有演员信息
- `data/categories.json`: 包含所有分类信息
- `data/spider.log`: 爬虫运行日志

### 测试爬取输出
- `data/test_dramas.json`: 包含测试短剧的详细信息
- `data/test_actors.json`: 包含测试演员信息
- `data/test_categories.json`: 包含测试分类信息

## 技术栈

- Python 3.7+
- requests: HTTP请求库
- BeautifulSoup4: HTML解析库
- dataclasses: 数据模型定义
- logging: 日志记录

## 扩展功能

可以根据需要扩展以下功能：

1. 添加数据库存储支持
2. 实现分布式爬取
3. 添加数据分析和可视化
4. 集成到现有的短剧数据平台
