import requests
import json
import os
import datetime

# 构建 actors_map 全局变量
with open('duanju_actors.json', 'r', encoding='utf-8') as f:
    actors_list = json.load(f)
actors_map = {f"{actor['name']}|{actor['avatar_url']}": actor for actor in actors_list}

# 构建 category_map 全局变量
with open('duanju_categories.json', 'r', encoding='utf-8') as f:
  categories_list = json.load(f)
category_map = {}
category_name_map = {}
for cat in categories_list:
  cat_id = cat.get('cat_id')
  name = cat.get('name')
  if cat_id:
    category_map[cat_id] = cat
  if name:
    category_name_map[name] = cat

# 打印 category_map 和 category_name_map 以便调试
print("Category Map:", json.dumps(category_map, ensure_ascii=False, indent=2))
print("Category Name Map:", json.dumps(category_name_map, ensure_ascii=False, indent=2))




# 构建 author_map 全局变量
with open('duanju_authors.json', 'r', encoding='utf-8') as f:
    authors_list = json.load(f)
author_map = {author['name']: author for author in authors_list}

def upload_attachment(filename):
  url = "https://data.wansu.tech/api/attachments:create?attachmentField=duanju.thumb_url"
  file_path = f'/Users/<USER>/Documents/短剧/皮皮虾/封面图/{filename}'
  files = [
    ('file', (filename, open(file_path, 'rb'), 'image/webp'))
  ]
  headers = {
    'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    'Accept': "application/json, text/plain, */*",
    'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
    'authorization': "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NDk3MzUxMDAyMTQsImlhdCI6MTc0OTgyMTUwMCwiZXhwIjoxNzQ5OTA3OTAwLCJqdGkiOiJmNGM1MTk3YS02Y2I3LTQ1YmMtYjhiNC00ZDFiYTJmYTA1MDcifQ.0Iwht03Mp8J9VRcaOptQhxmI60IGBlijfawy0YDzmiQ",
    'origin': "https://data.wansu.tech",
    'priority': "u=1, i",
    'referer': "https://data.wansu.tech/admin/y87iopxjbpn",
    'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    'sec-ch-ua-mobile': "?0",
    'sec-ch-ua-platform': "\"macOS\"",
    'sec-fetch-dest': "empty",
    'sec-fetch-mode': "cors",
    'sec-fetch-site': "same-origin",
    'x-authenticator': "basic",
    'x-hostname': "data.wansu.tech",
    'x-locale': "zh-CN",
    'x-role': "root",
    'x-timezone': "+08:00",
    'x-with-acl-meta': "true"
  }
  with open(file_path, 'rb') as f:
    files = [('file', (filename, f, 'image/webp'))]
    response = requests.post(url, files=files, headers=headers)
  if response.status_code != 200:
    print(f"上传失败: {response.status_code} - {response.text}")
    return None
  return response.text

def create_duanju(
  abstract,
  actors,
  author,
  book_name,
  book_id,
  category_tags,
  category,
  serial_count,
  read_count,
  read_count_last_updated_ts,
  thumb_url
):
  url = "https://data.wansu.tech/api/duanju:create"
  payload = {
    "abstract": abstract,
    "actors": actors,
    "author": author,
    "book_name": book_name,
    "book_id": book_id,
    "category_tags": category_tags,
    "platform": "ppx",
    "category": category,
    "serial_count": serial_count,
    "read_count": read_count,
    "read_count_last_updated_ts": read_count_last_updated_ts,
    "thumb_url": thumb_url
  }
  # 打印 payload 以便调试
  # print("Payload:", json.dumps(payload, ensure_ascii=False, indent=2))
  headers = {
    'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    'Accept': "application/json, text/plain, */*",
    'Content-Type': "application/json",
    'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
    'authorization': "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NDk3MzUxMDAyMTQsImlhdCI6MTc0OTgyMTUwMCwiZXhwIjoxNzQ5OTA3OTAwLCJqdGkiOiJmNGM1MTk3YS02Y2I3LTQ1YmMtYjhiNC00ZDFiYTJmYTA1MDcifQ.0Iwht03Mp8J9VRcaOptQhxmI60IGBlijfawy0YDzmiQ",
    'origin': "https://data.wansu.tech",
    'priority': "u=1, i",
    'referer': "https://data.wansu.tech/admin/y87iopxjbpn",
    'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    'sec-ch-ua-mobile': "?0",
    'sec-ch-ua-platform': "\"macOS\"",
    'sec-fetch-dest': "empty",
    'sec-fetch-mode': "cors",
    'sec-fetch-site': "same-origin",
    'x-authenticator': "basic",
    'x-hostname': "data.wansu.tech",
    'x-locale': "zh-CN",
    'x-role': "root",
    'x-timezone': "+08:00",
    'x-with-acl-meta': "true"
  }
  response = requests.post(url, data=json.dumps(payload), headers=headers)
  return response.text


# 读取 /Users/<USER>/Documents/短剧/皮皮虾/列表 目录下的所有 json 文件并循环解析
list_dir = '/Users/<USER>/Documents/短剧/皮皮虾/列表'
json_files = [f for f in os.listdir(list_dir) if f.endswith('.json')]
detail_dir = '/Users/<USER>/Documents/短剧/皮皮虾/详情'

# 新增：book_id 映射，记录已成功创建的 book_id
created_book_ids = set()
created_book_ids_path = '/Users/<USER>/python/created_book_ids.json'
if os.path.exists(created_book_ids_path):
    with open(created_book_ids_path, 'r', encoding='utf-8') as f:
        try:
            created_book_ids = set(json.load(f))
        except Exception:
            created_book_ids = set()

save_created_book_ids_counter = 0  # 新增：计数器
SAVE_CREATED_BOOK_IDS_INTERVAL = 10  # 每10次写一次文件

def save_created_book_ids(force=False):
    global save_created_book_ids_counter
    if force or save_created_book_ids_counter >= SAVE_CREATED_BOOK_IDS_INTERVAL:
        with open(created_book_ids_path, 'w', encoding='utf-8') as f:
            json.dump(list(created_book_ids), f, ensure_ascii=False, indent=2)
        save_created_book_ids_counter = 0

for json_file in json_files:
    print(f"处理列表文件: {json_file}")
    json_path = os.path.join(list_dir, json_file)
    with open(json_path, 'r', encoding='utf-8') as f:
        list_json = json.load(f)
    feed_data = list_json['data'][0].get('feed_data', {})
    if 'book_data' not in feed_data:
        print(f"文件 {json_file} 缺少 'book_data' 字段，跳过。")
        continue
    book_data_list = feed_data['book_data']
    for book_item in book_data_list:
        book = book_item['book_data']
        book_id = book['book_id']
        book_name = book['book_name']
        # 新增：如果 book_id 已经创建过，跳过
        if book_id in created_book_ids:
            print(f"book_id {book_id} 已经创建，跳过。")
            continue
        print(f"处理 book_id: {book_id}, book_name: {book_name}")
        # 读取详情目录下对应 book_id 的 json 文件
        detail_json_path = os.path.join(detail_dir, f'{book_id}.json')
        if os.path.exists(detail_json_path):
            print(f"读取详情文件: {detail_json_path}")
            with open(detail_json_path, 'r', encoding='utf-8') as f:
                detail_json = json.load(f)
            abstract = detail_json['data']['abstract']
            author_name = detail_json['data'].get('author')
            author = []
            if author_name and author_name in author_map:
                author.append(author_map[author_name])
                print(f"找到作者: {author_name}")
            else:
                print(f"未找到作者: {author_name}")
        else:
            print(f"详情文件不存在: {detail_json_path}")
            abstract = None
            author = []
        # category_tags 构建（从详情 JSON.data.category_tags 读取）
        category_tags = []
        detail_category_tags = detail_json['data'].get('category_tags', [])
        for tag in detail_category_tags:
            tag_id = str(tag.get('category_id'))
            if tag_id in category_map:
              category_tags.append(category_map[tag_id])
              print(f"找到分类标签: {tag_id}")
            else:
              print(f"未找到分类标签: {tag_id}")
        # category 变量
        category_id = detail_json['data'].get('category_id', '')
        category_info = category_map.get(category_id, {})
        if category_info:
            print(f"找到主分类: {category_id}")
        else:
            print(f"未找到主分类: {category_id}")
        # actors 构建
        actors = []
        for actor in book.get('actor_list', []):
            avatar_url = actor.get('avatar_url', '')
            avatar_id = avatar_url.split('/')[-1].split('~')[0]
            key = f"{actor['name']}|{avatar_id}"
            if key in actors_map:
                actors.append(actors_map[key])
                print(f"找到演员: {key}")
            else:
                print(f"未找到演员: {key}")

        # serial_count, read_count
        serial_count = book.get('serial_count')
        read_count = book.get('read_count')
        # 当前时间，格式化为 ISO 8601
        read_count_last_updated_ts = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%S.000Z')
        # 上传封面图附件逻辑
        cover_dir = '/Users/<USER>/Documents/短剧/皮皮虾/封面图'
        cover_path = os.path.join(cover_dir, f'{book_id}.webp')
        thumb_url = []
        if os.path.exists(cover_path):
            print(f"找到封面图: {cover_path}")
            # 假设 upload_attachment 返回的是所需的 dict
            result = upload_attachment(f'{book_id}.webp')
            if result:
                # result 是字符串，需要解析为 JSON 对象
                try:
                  result_json = json.loads(result)
                  # 只取 data 字段作为 thumb_url
                  thumb_url = [result_json.get("data", {})]
                except Exception as e:
                  print(f"封面图上传返回解析失败: {e}")
                  thumb_url = []
                print(f"封面图上传成功: {result}")
            else:
                print(f"封面图上传失败: {book_id}.webp")
        else:
            print(f"封面图不存在: {cover_path}")
        # 调用 create_duanju 函数
        try:
            resp = create_duanju(
                book_id=book_id,
                book_name=book_name,
                abstract=abstract,
                author=author,
                category_tags=category_tags,
                actors=actors,
                category=category_info,
                serial_count=serial_count,
                read_count=read_count,
                read_count_last_updated_ts=read_count_last_updated_ts,
                thumb_url=thumb_url
            )
            # 解析返回的 JSON 数据
            resp = json.loads(resp)
            # 检查返回数据是否包含预期字段
            if not isinstance(resp, dict):
                print(f"创建短剧返回异常: {resp}")
                continue
            # 检查是否包含 data 字段和预期的 id 和 book_name
            if resp and 'data' in resp and 'id' in resp['data'] and 'book_name' in resp['data']:
                print(f"创建短剧成功: id={resp['data']['id']}, book_name={resp['data']['book_name']}")
                # 新增：记录已创建的 book_id 并周期性保存
                created_book_ids.add(book_id)
                save_created_book_ids_counter += 1
                save_created_book_ids()
            else:
                print(f"创建短剧返回异常: {resp}")
        except Exception as e:
            print(f'create_duanju 调用失败: {e}')

# 新增：循环结束后强制保存一次
save_created_book_ids(force=True)
