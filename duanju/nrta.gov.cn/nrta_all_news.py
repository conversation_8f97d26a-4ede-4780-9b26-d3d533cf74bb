from time import sleep
import requests

from nrta_news_list import fetch_nrta_news_list_data
import json
import random

all_records = []
start = 1
end = 45
max_start = 1486
max_end = 1529
perpage = 15
while start <= max_start and end <= max_end:
    print(f"Fetching: start={start}, end={end}, perpage={perpage}")
    data = fetch_nrta_news_list_data(start, end, perpage)
    print(f"Fetched {len(data.records)} records.")
    all_records.extend([r.to_dict() for r in data.records])
    start += 45
    end += 45
    sleep_time = random.uniform(1, 2)
    print(f"Sleeping for {sleep_time:.2f} seconds...")
    sleep(sleep_time)

result = {
    'totalrecord': data.totalrecord,
    'totalpage': data.totalpage,
    'records': all_records
}
with open('nrta_all_news_records.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)
print("All records saved to nrta_all_news_records.json")