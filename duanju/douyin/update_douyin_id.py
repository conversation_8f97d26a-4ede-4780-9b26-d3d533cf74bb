import json
import os
import time
from typing import List, Dict
from duanju.douyin.search_user import search_user

INPUT_JSON = "duanju_actors.json"
OUTPUT_JSON = "duanju_actors_with_douyin.json"
OUTPUT_SQL = "update.sql"


def main():
    # 读取已爬取数据
    crawled_actors = {}
    if os.path.exists(OUTPUT_JSON):
        with open(OUTPUT_JSON, "r", encoding="utf-8") as f:
            try:
                old_results = json.load(f)
                for a in old_results:
                    if a.get("id") and a.get("douyin_id"):
                        crawled_actors[a["id"]] = a
            except Exception:
                pass
    # 读取原始数据
    with open(INPUT_JSON, "r", encoding="utf-8") as f:
        actors: List[Dict] = json.load(f)

    results = []
    sql_lines = []
    # 先把已爬取的加入结果和SQL
    for actor in actors:
        actor_id = actor.get("id")
        if actor_id in crawled_actors:
            results.append(crawled_actors[actor_id])
            douyin_id = crawled_actors[actor_id].get("douyin_id", "")
            if douyin_id:
                sql_lines.append(f'update duanju_actors set douyin_id = "{douyin_id}" where id = {actor_id};')

    print(f"已爬取的抖音用户数量: {len(crawled_actors)}")

    # 处理未爬取的
    for actor in actors:
        name = actor.get("name")
        actor_id = actor.get("id")
        if not name or not actor_id or actor_id in crawled_actors:
            continue
        try:
            resp = search_user(name)
            print(f"Searching Douyin for {name} ({actor_id}) - {resp}")
            actor["douyin_search_resp"] = resp  # 存储完整API返回
            # 不再提取douyin_id和url，后续离线分析
        except Exception as e:
            actor["douyin_search_resp"] = {"error": str(e)}
            print(f"Error for {name}: {e}")
        results.append(actor)
        # 实时写入文件
        with open(OUTPUT_JSON, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        # SQL文件不再实时写入（可选）
        time.sleep(12)  # 限流

    # 最终再写一遍，确保完整
    with open(OUTPUT_JSON, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    with open(OUTPUT_SQL, "w", encoding="utf-8") as f:
        f.write("\n".join(sql_lines))

if __name__ == "__main__":
    main()
