# 短剧演员视频批量上传处理器

这个工具用于批量处理短剧演员的视频上传，包括提取视频封面、上传文件和创建短剧cut记录。

## 功能特性

1. **视频封面提取**：使用FFmpeg从MP4视频文件中提取首帧作为封面图片
2. **文件上传**：支持封面图片和视频文件的批量上传
3. **API集成**：自动调用API创建短剧cut记录
4. **错误处理**：完善的错误处理和重试机制
5. **进度跟踪**：详细的日志记录和处理结果保存
6. **灵活配置**：支持多种运行模式和参数配置

## 环境要求

- Python 3.7+
- FFmpeg (用于视频处理)
- 网络连接 (用于API调用)

## 安装依赖

```bash
pip install -r batch_upload_requirements.txt
```

确保系统已安装FFmpeg：

```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# Windows
# 下载FFmpeg并添加到PATH环境变量
```

## 使用方法

### 1. 环境检查

```bash
python test_environment.py
```

### 2. 列出所有演员信息

```bash
python run_batch_upload.py --list-actors
```

### 3. 测试模式（处理前3个有视频的演员）

```bash
python run_batch_upload.py --test
```

### 4. 处理指定演员

```bash
# 按演员ID处理（只处理第一个视频）
python run_batch_upload.py --actor-id 33

# 按kuaidong_id处理（只处理第一个视频）
python run_batch_upload.py --kuaidong-id 7387251624085438474

# 处理指定演员的所有视频
python run_batch_upload.py --kuaidong-id 7387251624085438474 --all-videos
```

### 5. 批量处理

```bash
# 处理所有有视频的演员（每个演员只处理第一个视频）
python run_batch_upload.py

# 处理所有有视频的演员的所有视频
python run_batch_upload.py --all-videos

# 处理指定范围的演员
python run_batch_upload.py --start 0 --end 10

# 包括没有视频文件的演员（会跳过但记录到结果中）
python run_batch_upload.py --include-no-videos
```

### 6. 交互式示例

```bash
python usage_examples.py
```

## 文件结构

```
duanju/kuaidong/
├── duanju_actors_with_kuaidong.json    # 演员数据文件
├── kuaidong_data/                      # 视频数据目录
│   └── {kuaidong_id}/
│       └── videos/
│           └── *.mp4                   # 视频文件
├── temp/                               # 临时文件目录
├── batch_upload_processor.py           # 主处理器
├── run_batch_upload.py                 # 运行脚本
├── config.py                           # 配置文件
└── README.md                           # 说明文档
```

## 处理流程

对于每个演员，脚本会执行以下步骤：

1. **查找视频文件**：在 `kuaidong_data/{kuaidong_id}/videos/` 目录下查找MP4文件
2. **提取封面**：使用FFmpeg从视频首帧提取PNG格式的封面图片
3. **上传封面**：调用API上传封面图片
4. **上传视频**：调用API上传视频文件
5. **创建Cut**：使用上传后的文件信息创建短剧cut记录
6. **清理临时文件**：删除临时生成的封面图片

## 输出文件

- **日志文件**：`batch_upload.log` - 详细的处理日志
- **结果文件**：`batch_upload_results_{timestamp}.json` - 处理结果汇总

## 配置说明

可以通过修改 `config.py` 文件来调整各种参数：

- **API配置**：认证token、超时时间等
- **文件路径**：数据目录、临时目录等
- **处理参数**：延迟时间、重试次数等
- **Cut默认值**：描述、标题模板等

## 错误处理

脚本包含完善的错误处理机制：

- 文件不存在时会记录错误并跳过
- API调用失败时会记录详细错误信息
- 网络超时会自动重试
- 所有错误都会记录到日志文件中

## 注意事项

1. **API Token**：确保config.py中的认证token有效且未过期
2. **文件权限**：确保脚本有读取视频文件和写入临时文件的权限
3. **网络连接**：上传大文件时需要稳定的网络连接
4. **磁盘空间**：确保有足够的临时空间存储提取的封面图片
5. **API限流**：脚本在处理每个演员后会有1秒延迟，避免API限流

## 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```
   解决方案：安装FFmpeg并确保在PATH环境变量中
   ```

2. **API认证失败**
   ```
   解决方案：检查config.py中的auth_token是否正确和有效
   ```

3. **视频文件未找到**
   ```
   解决方案：检查kuaidong_data目录结构是否正确
   ```

4. **上传超时**
   ```
   解决方案：增加config.py中的timeout值或检查网络连接
   ```

## 示例输出

```
2025-07-22 19:00:00 - INFO - 成功加载 51 个演员数据
2025-07-22 19:00:01 - INFO - 开始批量处理，共 3 个演员 (索引 0 到 2)
2025-07-22 19:00:01 - INFO - 进度: 1/3
2025-07-22 19:00:01 - INFO - 开始处理演员: 于龙 (ID: 23, kuaidong_id: 7418013204671086607)
2025-07-22 19:00:02 - INFO - 成功提取视频首帧: kuaidong_data/7418013204671086607/videos/video.mp4 -> temp/7418013204671086607_cover.png
2025-07-22 19:00:05 - INFO - 文件上传成功: 7418013204671086607_cover.png
2025-07-22 19:00:15 - INFO - 文件上传成功: video.mp4
2025-07-22 19:00:16 - INFO - 成功创建cut: actor_id=23
2025-07-22 19:00:16 - INFO - 演员 于龙 处理完成
```
