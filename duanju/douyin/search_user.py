
import requests
from typing import Any, Dict

def search_user(query: str) -> Dict[str, Any]:
    """
    调用本地接口进行抖音用户搜索。
    :param query: 搜索关键词
    :param limit: 返回结果数量
    :return: 接口返回的 JSON 数据
    """
    # url = "http://100.64.249.38:3002/v1/search"
    url = "https://api.firecrawl.dev/v1/search"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer fc-ba79f6a4b90a49e9ba74df04d915f77d"
    }
    data = {
        "query": query + " 短剧 演员",
        "limit": 5,
        "location": "",
        "tbs": "",
        "scrapeOptions": {
            "formats": [ "markdown", "links" ]
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()
