import frida
import sys
import time
from ppadb.client import Client as AdbClient

# 1. 通过 pure-python-adb 启动目标 App
def start_app_with_adb(package: str, activity: str):
    """使用 pure-python-adb 启动应用"""
    client = AdbClient(host="127.0.0.1", port=5037)
    device = client.devices()[0]  # 获取第一个设备
    
    # 启动应用
    device.shell(f"am start -n {package}/{activity}")
    print(f"已启动应用: {package}/{activity}")
    
    # 返回设备序列号（用于Frida连接）
    return device.serial

# 2. Frida Hook 脚本（协议降级）
def run_frida_hook(device_id: str, package_name: str):
    """注入Frida脚本实现协议降级"""
    hook_script = """
    Interceptor.attach(Module.findExportByName(null, "SSL_CTX_set_min_proto_version"), {
        onEnter: function (args) {
            console.log("[*] SSL_CTX_set_min_proto_version called");

            // 强制设置最低协议版本为 TLS 1.0
            args[1] = 0x0301; // TLS 1.0 的版本号（OpenSSL 中的定义）
        },
        onLeave: function (retval) {
            console.log("[*] SSL_CTX_set_min_proto_version returned:", retval);
        }
    });
    """

    try:
        # 附加到目标进程
        device = frida.get_device(device_id)
        session = device.attach(package_name)
        script = session.create_script(hook_script)
        
        # 打印Frida日志
        def on_message(message, data):
            print(f"[Frida] {message}")
        
        script.on('message', on_message)
        script.load()
        print("Frida脚本注入成功！")
        
        # 保持脚本运行
        sys.stdin.read()
    except Exception as e:
        print(f"Frida注入失败: {str(e)}")

if __name__ == "__main__":
    # 配置目标App信息
    TARGET_PACKAGE = "com.phoenix.read"  # 替换为实际包名
    TARGET_ACTIVITY = "com.dragon.read.pages.splash.SplashActivity"  # 替换为主Activity
    
    try:
        # 步骤1：启动App并获取设备ID
        device_id = start_app_with_adb(TARGET_PACKAGE, TARGET_ACTIVITY)
        
        # 等待App启动完成
        time.sleep(5)
        
        # 步骤2：注入Frida脚本
        run_frida_hook(device_id, "com.phoenix.read:push")
    except IndexError:
        print("错误：未找到连接的设备！")
    except KeyboardInterrupt:
        print("程序已终止")