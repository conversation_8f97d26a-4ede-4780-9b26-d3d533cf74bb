# 快懂百科搜索工具

这是一个使用 Playwright 自动化工具来模拟用户输入，获取快懂百科网站搜索建议数据的项目。

## 功能特性

- 🔍 模拟用户在快懂百科网站上的搜索输入
- 🚫 拦截搜索API请求，阻止发出并提取关键参数
- 🎯 提取动态生成的 msToken 和 a_bogus 参数
- 📤 记录 POST 请求体内容
- 📝 支持命令行交互式使用
- 📋 详细的操作日志记录和管理
- ⚡ 等待所有 JavaScript 加载完毕确保稳定性

## 快速开始

### 1. 安装依赖

```bash
cd experiment/kuaidong
npm install
```

### 2. 安装浏览器

```bash
npm run install-browsers
```

### 3. 运行测试

```bash
npm test
```

### 4. 开始使用

```bash
# 交互式搜索
npm run search

# 直接搜索指定关键词
npm run search -- "王小亿"

# 批量搜索
npm run search -- --batch "王小亿" "演员" "中国短剧"
```

## 使用方法

### 命令行模式

```bash
# 交互式搜索模式
npm run search

# 搜索单个关键词
npm run search -- "王小亿"

# 批量搜索多个关键词
npm run search -- --batch "王小亿" "演员" "中国短剧"

# 交互模式
npm run search -- --interactive
```

### 编程接口

#### 基础使用

```javascript
import { BaikeSearcher } from './src/searcher.js';

const searcher = new BaikeSearcher({
  headless: true,  // 无头模式
  timeout: 30000   // 30秒超时
});

try {
  // 搜索单个关键词
  const results = await searcher.search('王小亿');
  console.log(results);

  // 批量搜索
  const batchResults = await searcher.batchSearch(['王小亿', '演员']);
  console.log(batchResults);

} finally {
  await searcher.close();
}
```

#### 高级配置

```javascript
const searcher = new BaikeSearcher({
  headless: false,  // 显示浏览器窗口
  timeout: 60000,   // 60秒超时
});
```

## 项目结构

```
experiment/kuaidong/
├── package.json          # 项目配置
├── README.md             # 项目说明
├── .gitignore            # Git忽略文件
├── src/
│   ├── index.js          # 主入口文件
│   ├── cli.js            # 命令行接口
│   ├── searcher.js       # 核心搜索功能
│   ├── logger.js         # 日志记录
│   └── test.js           # 测试文件
├── examples/             # 使用示例
│   ├── basic-usage.js    # 基础使用示例
│   └── advanced-usage.js # 高级使用示例
└── logs/                 # 操作日志目录（自动创建）
```

## 输出格式

工具会拦截API请求并返回如下格式的数据：

```json
{
  "url": "https://www.baike.com/api/v2/search/getDocSugDataV2?msToken=...&a_bogus=...",
  "msToken": "ayOV8JKQCxbQCQ-ay-HUUCM_-ddFZwPGNOMrEaZF_rcWxeDOpBpEaskVuVfBPtMrcoUAYDESp0Tqp1uVDnhf2BYHZALBypY0M3Ov7uwX0MC2zIKo049mWjAwX6s9HI0DmGg%3D",
  "aBogus": "YJ0QXO2aMsm1JFWrc7ki9nHw0H80YW-DgZENcUsoz0Lb",
  "method": "POST",
  "postData": "{\"args\":[{\"Query\":\"王小亿\",\"Offset\":0,\"Count\":5}]}",
  "intercepted": true
}
```

## 运行示例

```bash
# 运行基础示例
node examples/basic-usage.js

# 运行高级示例
node examples/advanced-usage.js
```

## 日志管理

所有操作都会自动记录到 `logs/` 目录下，文件名格式为：
- `search_YYYY-MM-DDTHH-mm-ss-sssZ.log` - 搜索操作日志
- `error_YYYY-MM-DDTHH-mm-ss-sssZ.log` - 错误日志

## API 参考

### BaikeSearcher 类

#### 构造函数选项

- `headless` (boolean): 是否使用无头模式，默认 `true`
- `timeout` (number): 操作超时时间（毫秒），默认 `30000`

#### 方法

- `search(keyword)`: 搜索单个关键词
- `batchSearch(keywords)`: 批量搜索多个关键词
- `init()`: 初始化浏览器（自动调用）
- `close()`: 关闭浏览器

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   npm run install-browsers
   ```

2. **网络连接问题**
   - 确保能够访问 https://www.baike.com/
   - 检查防火墙设置

3. **权限问题**
   ```bash
   chmod +x src/cli.js
   ```

### 调试模式

设置 `headless: false` 可以看到浏览器操作过程：

```javascript
const searcher = new BaikeSearcher({ headless: false });
```

## 注意事项

- 需要 Node.js 18+ 版本
- 首次使用需要安装 Playwright 浏览器
- 网络连接需要能够访问 baike.com
- 建议在搜索间隔中添加适当延迟避免请求过快
- 操作日志会自动保存到 `logs/` 目录
