# 演员数据更新脚本

## 功能说明
该脚本用于处理演员数据的插入和更新操作，实现"先查找后更新，如果找不到则插入"的逻辑。

## 文件说明
- `update_actors.py` - 主要的Python脚本
- `toscraped.csv` - 包含需要更新的演员数据的CSV文件
- `requirements.txt` - Python依赖包列表
- `update_actors.log` - 运行日志文件（运行后生成）

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法
1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 测试数据库连接（可选）：
```bash
python test_connection.py
```

3. 确保 `toscraped.csv` 文件在同一目录下

4. 运行主脚本：
```bash
python update_actors.py
```

## 脚本功能
1. **读取CSV文件**: 从 `toscraped.csv` 中提取演员姓名
2. **数据库连接**: 连接到指定的MySQL数据库
3. **查找演员**: 根据姓名查找演员是否已存在
4. **更新或插入**:
   - 如果演员存在：更新 `need_scrape` 字段为1，并更新 `updated_at` 时间戳
   - 如果演员不存在：插入新记录，设置 `need_scrape` 为1，并设置创建和更新时间戳

## 数据库配置
脚本中已配置数据库连接信息：
- 主机: **************
- 端口: 3306
- 用户: root
- 密码: yuanshu@nb
- 数据库: huawaiyin

## 日志记录
脚本会生成详细的日志记录，包括：
- 处理进度
- 成功/失败统计
- 错误信息
- 日志同时输出到控制台和 `update_actors.log` 文件

## 错误处理
- 数据库连接失败时会自动重试
- 处理过程中出现错误会回滚事务
- 详细的错误信息会记录在日志中

## 注意事项
1. 运行前请确保数据库连接正常
2. 建议在测试环境先验证脚本功能
3. 脚本会自动处理事务，确保数据一致性
4. CSV文件中的演员姓名会自动去除首尾空格
