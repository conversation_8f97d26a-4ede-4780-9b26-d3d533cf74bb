#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快懂百科数据提取器
专注于提取 __prefetch_doc_data__ 变量并处理相关数据
"""

import json
import os
import re
import time
import asyncio
import aiohttp
import aiofiles
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse

from playwright.async_api import async_playwright, <PERSON>, Browser
from bs4 import BeautifulSoup


class KuaidongDataExtractor:
    def __init__(self, data_dir: str = "kuaidong_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)

        self.browser: Optional[Browser] = None
        self.session: Optional[aiohttp.ClientSession] = None

    def create_actor_directories(self, kuaidong_id: str) -> Dict[str, Path]:
        """为每个演员创建独立的目录结构"""
        actor_dir = self.data_dir / kuaidong_id
        actor_dir.mkdir(exist_ok=True)

        # 创建子目录
        directories = {
            'json_dir': actor_dir / "json",
            'images_dir': actor_dir / "images",
            'videos_dir': actor_dir / "videos",
            'html_dir': actor_dir / "html"
        }

        for dir_path in directories.values():
            dir_path.mkdir(exist_ok=True)

        return directories

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        if self.browser:
            await self.browser.close()

    async def init_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        )

    async def create_page(self) -> Page:
        """创建新页面"""
        if not self.browser:
            await self.init_browser()
        
        page = await self.browser.new_page()
        
        # 设置用户代理
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                         '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # 设置视口
        await page.set_viewport_size({"width": 1920, "height": 1080})
        
        return page

    async def extract_prefetch_data(self, html_content: str) -> Optional[Dict]:
        """从 HTML 中提取 __prefetch_doc_data__ 变量"""
        try:
            # 多种模式匹配 __prefetch_doc_data__
            patterns = [
                r'var\s+__prefetch_doc_data__\s*=\s*({.*?});',
                r'window\.__prefetch_doc_data__\s*=\s*({.*?});',
                r'__prefetch_doc_data__\s*=\s*({.*?});',
                r'"__prefetch_doc_data__"\s*:\s*({.*?})',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                if matches:
                    json_str = matches[0]
                    try:
                        # 清理 JSON 字符串
                        json_str = json_str.strip()
                        if json_str.endswith(','):
                            json_str = json_str[:-1]
                        
                        data = json.loads(json_str)
                        print(f"成功提取 __prefetch_doc_data__，数据大小: {len(json_str)} 字符")
                        return data
                    except json.JSONDecodeError as e:
                        print(f"JSON 解析失败: {e}")
                        continue
            
            print("未找到 __prefetch_doc_data__ 变量")
            return None
            
        except Exception as e:
            print(f"提取 __prefetch_doc_data__ 失败: {e}")
            return None

    async def save_origin_json(self, data: Dict, kuaidong_id: str, directories: Dict[str, Path]) -> str:
        """保存原始 JSON 数据"""
        filename = f"{kuaidong_id}.origin.json"
        filepath = directories['json_dir'] / filename

        async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data, ensure_ascii=False, indent=2))

        print(f"原始 JSON 已保存: {filepath}")
        return str(filepath)

    async def extract_and_save_data(self, origin_data: Dict, kuaidong_id: str, directories: Dict[str, Path]) -> Dict:
        """提取指定字段并保存为 saved.json"""
        try:
            extracted_data = {}

            # 提取 DocMeta.Title
            if 'DocMeta' in origin_data and 'Title' in origin_data['DocMeta']:
                extracted_data['title'] = origin_data['DocMeta']['Title']

            # 提取 VersionContent 中的各个字段
            if 'VersionContent' in origin_data:
                version_content = origin_data['VersionContent']

                # HeadImageList
                if 'HeadImageList' in version_content:
                    extracted_data['head_image_list'] = version_content['HeadImageList']

                # Infobox - 原始数据和解析后的数据
                if 'Infobox' in version_content:
                    extracted_data['infobox'] = version_content['Infobox']
                    # 解析 infobox 为结构化数据
                    extracted_data['infobox_parsed'] = self.extract_infobox_data(version_content['Infobox'])

                # Content - 原始数据和转换为 markdown
                if 'Content' in version_content:
                    extracted_data['content'] = version_content['Content']
                    # 转换为 markdown 格式
                    extracted_data['content_markdown'] = self.convert_content_to_markdown(version_content['Content'])

                # ReferenceList
                if 'ReferenceList' in version_content:
                    extracted_data['reference_list'] = version_content['ReferenceList']

                # RelationshipList
                if 'RelationshipList' in version_content:
                    extracted_data['relationship_list'] = version_content['RelationshipList']

                # ImageMap
                if 'ImageMap' in version_content:
                    extracted_data['image_map'] = version_content['ImageMap']

                # VideoList
                if 'VideoList' in version_content:
                    extracted_data['video_list'] = version_content['VideoList']

            # 保存提取的数据
            filename = f"{kuaidong_id}.saved.json"
            filepath = directories['json_dir'] / filename

            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(extracted_data, ensure_ascii=False, indent=2))

            print(f"提取数据已保存: {filepath}")
            print(f"提取字段数量: {len(extracted_data)}")

            return extracted_data

        except Exception as e:
            print(f"数据提取失败: {e}")
            return {}

    async def download_image(self, image_url: str, filename: str, images_dir: Path) -> Optional[str]:
        """下载图片"""
        if not image_url:
            return None

        try:
            # 处理相对URL和协议相对URL
            if image_url.startswith('//'):
                image_url = 'https:' + image_url
            elif not image_url.startswith('http'):
                image_url = urljoin('https://www.baike.com', image_url)

            async with self.session.get(image_url) as response:
                if response.status == 200:
                    content = await response.read()

                    # 统一使用 .png 后缀
                    full_filename = f"{filename}.png"
                    filepath = images_dir / full_filename

                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)

                    print(f"图片已下载: {filepath}")
                    return str(filepath)
        except Exception as e:
            print(f"下载图片失败 {image_url}: {e}")

        return None

    def extract_infobox_data(self, infobox_json_str: str) -> Dict:
        """从 infobox JSON 字符串中提取基础信息"""
        try:
            if not infobox_json_str:
                print("infobox 数据为空")
                return {}

            # 解析 JSON 字符串
            infobox_data = json.loads(infobox_json_str)
            if not infobox_data or not isinstance(infobox_data, list):
                print("infobox 数据格式不正确")
                return {}

            extracted_info = {}
            current_key = None
            current_group = None

            for item in infobox_data:
                if not isinstance(item, dict):
                    continue

                item_type = item.get('type', '')

                if item_type == 'infobox_group':
                    # 信息组标题
                    children = item.get('children', [])
                    if children and isinstance(children[0], dict) and 'text' in children[0]:
                        current_group = children[0]['text']

                elif item_type == 'infobox_key':
                    # 信息键
                    children = item.get('children', [])
                    if children and isinstance(children[0], dict) and 'text' in children[0]:
                        current_key = children[0]['text']

                elif item_type == 'infobox_value' and current_key:
                    # 信息值
                    value_text = self._extract_text_from_children(item.get('children', []))
                    if value_text and value_text.strip():
                        # 清理文本，移除多余空格和引用标记
                        clean_value = re.sub(r'\s+', ' ', value_text).strip()
                        clean_value = re.sub(r'\[\d+\]', '', clean_value)  # 移除引用标记

                        if current_group:
                            group_key = f"{current_group}_{current_key}"
                        else:
                            group_key = current_key

                        extracted_info[group_key] = clean_value
                    current_key = None

            print(f"从 infobox 提取到 {len(extracted_info)} 个字段")
            return extracted_info

        except Exception as e:
            print(f"解析 infobox 失败: {e}")
            return {}

    def _extract_text_from_children(self, children: List) -> str:
        """递归提取子节点中的文本内容"""
        if not children:
            return ""

        text_parts = []

        for child in children:
            if isinstance(child, dict):
                if child.get('type') == 'text':
                    text_parts.append(child.get('text', ''))
                elif child.get('type') == 'paragraph':
                    # 递归处理段落中的子节点
                    child_children = child.get('children')
                    if child_children is not None:
                        paragraph_text = self._extract_text_from_children(child_children)
                        text_parts.append(paragraph_text)
                elif child.get('type') == 'internal_link':
                    # 提取内部链接的文本
                    child_children = child.get('children')
                    if child_children is not None:
                        link_text = self._extract_text_from_children(child_children)
                        text_parts.append(link_text)
                elif child.get('type') == 'external_link':
                    # 提取外部链接的文本
                    child_children = child.get('children')
                    if child_children is not None:
                        link_text = self._extract_text_from_children(child_children)
                        text_parts.append(link_text)
                elif 'children' in child and child.get('children') is not None:
                    # 递归处理其他有子节点的元素
                    child_text = self._extract_text_from_children(child.get('children', []))
                    text_parts.append(child_text)

        return ''.join(text_parts)

    def convert_content_to_markdown(self, content_json_str: str) -> str:
        """将 content JSON 字符串转换为可读的 markdown 格式"""
        try:
            if not content_json_str:
                print("content 数据为空")
                return ""

            # 解析 JSON 字符串
            content_data = json.loads(content_json_str)
            if not content_data or not isinstance(content_data, list):
                print("content 数据格式不正确")
                return ""

            markdown_parts = []

            for item in content_data:
                if not isinstance(item, dict):
                    continue
                markdown_text = self._convert_item_to_markdown(item)
                if markdown_text and markdown_text.strip():
                    markdown_parts.append(markdown_text)

            # 合并所有部分，确保段落间有适当的空行
            result = '\n\n'.join(markdown_parts)

            # 清理多余的空行
            result = re.sub(r'\n{3,}', '\n\n', result)

            print(f"转换为 markdown，长度: {len(result)} 字符")
            return result

        except Exception as e:
            print(f"转换 content 为 markdown 失败: {e}")
            return ""

    def _convert_item_to_markdown(self, item: Dict) -> str:
        """将单个内容项转换为 markdown"""
        if not isinstance(item, dict):
            return ""

        item_type = item.get('type', '')

        if item_type == 'paragraph':
            # 段落
            text = self._extract_text_from_children(item.get('children', []))
            return text.strip()

        elif item_type == 'heading':
            # 标题
            level = item.get('attrs', {}).get('level', 1)
            text = self._extract_text_from_children(item.get('children', []))
            return f"{'#' * level} {text.strip()}"

        elif item_type == 'table':
            # 表格
            return self._convert_table_to_markdown(item)

        elif item_type == 'video':
            # 视频
            text = self._extract_text_from_children(item.get('children', []))
            video_id = item.get('attrs', {}).get('id', '')
            return f"**视频**: {text.strip()} (ID: {video_id})"

        elif item_type == 'text':
            # 纯文本
            return item.get('text', '')

        else:
            # 其他类型，尝试提取文本
            text = self._extract_text_from_children(item.get('children', []))
            return text.strip()

    def _convert_table_to_markdown(self, table_item: Dict) -> str:
        """将表格转换为 markdown 表格格式"""
        try:
            children = table_item.get('children', [])
            if not children:
                return ""

            markdown_rows = []
            header_processed = False

            for row in children:
                if row.get('type') == 'table_row':
                    row_cells = []
                    row_children = row.get('children', [])

                    for cell in row_children:
                        if cell.get('type') in ['table_header', 'table_cell']:
                            cell_text = self._extract_text_from_children(cell.get('children', []))
                            # 清理单元格文本，移除换行符
                            clean_text = re.sub(r'\s+', ' ', cell_text).strip()
                            row_cells.append(clean_text)

                    if row_cells:
                        # 构建表格行
                        markdown_row = '| ' + ' | '.join(row_cells) + ' |'
                        markdown_rows.append(markdown_row)

                        # 如果是第一行（表头），添加分隔符
                        if not header_processed:
                            separator = '| ' + ' | '.join(['---'] * len(row_cells)) + ' |'
                            markdown_rows.append(separator)
                            header_processed = True

            return '\n'.join(markdown_rows)

        except Exception as e:
            print(f"转换表格失败: {e}")
            return ""

    async def download_head_images(self, head_image_list: List, kuaidong_id: str, extracted_data: Dict, directories: Dict[str, Path]) -> Dict:
        """下载头像图片"""
        if not head_image_list:
            return extracted_data

        downloaded_images = []

        for i, image_info in enumerate(head_image_list):
            if isinstance(image_info, dict) and 'URL' in image_info:
                image_url = image_info['URL']
                filename = f"{kuaidong_id}_head_{i}"

                filepath = await self.download_image(image_url, filename, directories['images_dir'])
                if filepath:
                    downloaded_images.append({
                        'original_url': image_url,
                        'local_path': filepath,
                        'index': i
                    })

        # 将下载信息添加到提取数据中
        extracted_data['downloaded_head_images'] = downloaded_images
        print(f"头像下载完成，共 {len(downloaded_images)} 张")

        return extracted_data

    async def extract_social_ids(self, reference_list: List) -> Dict:
        """从引用列表中提取社交账号信息"""
        social_ids = {}

        if not reference_list:
            return social_ids

        # 社交平台域名和关键词映射
        social_platforms = {
            'douyin': {
                'domains': ['douyin.com', 'amemv.com'],
                'keywords': ['抖音', 'douyin', 'tiktok']
            },
            'weibo': {
                'domains': ['weibo.com', 'sina.com.cn'],
                'keywords': ['微博', 'weibo', '新浪微博']
            },
            'xiaohongshu': {
                'domains': ['xiaohongshu.com', 'xhscdn.com'],
                'keywords': ['小红书', 'xiaohongshu', 'redbook']
            },
            'bilibili': {
                'domains': ['bilibili.com', 'b23.tv'],
                'keywords': ['哔哩哔哩', 'bilibili', 'b站']
            },
            'kuaishou': {
                'domains': ['kuaishou.com', 'gifshow.com'],
                'keywords': ['快手', 'kuaishou']
            },
            'instagram': {
                'domains': ['instagram.com'],
                'keywords': ['instagram', 'ins']
            },
            'twitter': {
                'domains': ['twitter.com', 'x.com'],
                'keywords': ['twitter', 'x.com']
            },
            'facebook': {
                'domains': ['facebook.com', 'fb.com'],
                'keywords': ['facebook', 'fb']
            }
        }

        for ref in reference_list:
            if isinstance(ref, dict):
                # 获取URL和其他文本字段
                url = ref.get('URL', '').lower()
                title = ref.get('Title', '').lower()
                website_name = ref.get('WebsiteName', '').lower()

                # 组合所有文本字段
                all_text = f"{url} {title} {website_name}"

                # 匹配社交平台
                for platform, config in social_platforms.items():
                    matched = False

                    # 首先检查域名
                    for domain in config['domains']:
                        if domain in url:
                            if platform not in social_ids:
                                social_ids[platform] = []
                            social_ids[platform].append({
                                'url': ref.get('URL', ''),
                                'title': ref.get('Title', ''),
                                'website_name': ref.get('WebsiteName', ''),
                                'reference_date': ref.get('ReferenceDate', ''),
                                'match_type': 'domain',
                                'match_value': domain
                            })
                            matched = True
                            break

                    # 如果域名没匹配，再检查关键词
                    if not matched:
                        for keyword in config['keywords']:
                            if keyword in all_text:
                                if platform not in social_ids:
                                    social_ids[platform] = []
                                social_ids[platform].append({
                                    'url': ref.get('URL', ''),
                                    'title': ref.get('Title', ''),
                                    'website_name': ref.get('WebsiteName', ''),
                                    'reference_date': ref.get('ReferenceDate', ''),
                                    'match_type': 'keyword',
                                    'match_value': keyword
                                })
                                break

        print(f"提取到社交账号信息: {list(social_ids.keys())}")
        for platform, accounts in social_ids.items():
            print(f"  {platform}: {len(accounts)} 个账号")

        return social_ids

    async def download_video(self, video_url: str, filename: str, videos_dir: Path) -> Optional[str]:
        """下载单个视频"""
        if not video_url:
            return None

        try:
            async with self.session.get(video_url) as response:
                if response.status == 200:
                    content = await response.read()

                    # 获取文件扩展名
                    parsed_url = urlparse(video_url)
                    ext = os.path.splitext(parsed_url.path)[1] or '.mp4'

                    full_filename = f"{filename}{ext}"
                    filepath = videos_dir / full_filename

                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)

                    print(f"视频已下载: {filepath}")
                    return str(filepath)
        except Exception as e:
            print(f"下载视频失败 {video_url}: {e}")

        return None

    async def download_videos(self, video_list: List, kuaidong_id: str, extracted_data: Dict, directories: Dict[str, Path]) -> Dict:
        """下载视频列表"""
        if not video_list:
            return extracted_data

        downloaded_videos = []

        for i, video_info in enumerate(video_list):
            if isinstance(video_info, dict) and 'VideoH265URLList' in video_info:
                url_list = video_info['VideoH265URLList']

                # 优先使用 api.amemv.com 域名
                primary_urls = [url for url in url_list if 'api.amemv.com' in url]
                fallback_urls = [url for url in url_list if 'api-play.amemv.com' in url]

                # 尝试下载
                downloaded = False
                filename = f"{kuaidong_id}_video_{i}"

                # 先尝试主要域名
                for url in primary_urls:
                    filepath = await self.download_video(url, filename, directories['videos_dir'])
                    if filepath:
                        downloaded_videos.append({
                            'original_url': url,
                            'local_path': filepath,
                            'index': i,
                            'domain': 'api.amemv.com'
                        })
                        downloaded = True
                        break

                # 如果主要域名失败，尝试备选域名
                if not downloaded:
                    for url in fallback_urls:
                        filepath = await self.download_video(url, filename, directories['videos_dir'])
                        if filepath:
                            downloaded_videos.append({
                                'original_url': url,
                                'local_path': filepath,
                                'index': i,
                                'domain': 'api-play.amemv.com'
                            })
                            downloaded = True
                            break

                if not downloaded:
                    print(f"视频 {i} 下载失败，所有URL都无法访问")

        # 将下载信息添加到提取数据中
        extracted_data['downloaded_videos'] = downloaded_videos
        print(f"视频下载完成，共 {len(downloaded_videos)} 个")

        return extracted_data

    async def save_html(self, html_content: str, kuaidong_id: str, directories: Dict[str, Path]) -> str:
        """保存HTML页面"""
        filename = f"{kuaidong_id}.html"
        filepath = directories['html_dir'] / filename

        async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
            await f.write(html_content)

        return str(filepath)

    async def scrape_actor_detail(self, kuaidong_id: str, actor_name: str) -> Dict[str, Any]:
        """爬取单个演员的详情信息"""
        url = f"https://www.baike.com/wikiid/{kuaidong_id}"
        print(f"开始爬取演员: {actor_name} ({kuaidong_id})")

        # 为每个演员创建独立的目录结构
        directories = self.create_actor_directories(kuaidong_id)

        page = await self.create_page()

        try:
            # 访问页面
            print("正在加载页面...")
            await page.goto(url, wait_until='domcontentloaded', timeout=30000)

            # 等待页面完全加载
            print("等待页面完全加载...")
            await page.wait_for_timeout(8000)

            # 获取HTML内容
            html_content = await page.content()
            html_path = await self.save_html(html_content, kuaidong_id, directories)
            print(f"HTML已保存: {html_path}")

            # 提取 __prefetch_doc_data__
            print("提取 __prefetch_doc_data__ 变量...")
            prefetch_data = await self.extract_prefetch_data(html_content)

            if not prefetch_data:
                print("未找到 __prefetch_doc_data__ 变量，尝试从页面执行JavaScript获取...")
                # 尝试从页面直接获取
                prefetch_data = await page.evaluate("() => window.__prefetch_doc_data__ || null")

            if not prefetch_data:
                return {
                    'kuaidong_id': kuaidong_id,
                    'name': actor_name,
                    'url': url,
                    'error': '未找到 __prefetch_doc_data__ 变量',
                    'scraped_at': datetime.now().isoformat(),
                    'html_path': html_path
                }

            # 保存原始JSON
            origin_path = await self.save_origin_json(prefetch_data, kuaidong_id, directories)

            # 提取并保存指定数据
            print("提取指定字段...")
            extracted_data = await self.extract_and_save_data(prefetch_data, kuaidong_id, directories)

            # 下载头像图片
            if 'head_image_list' in extracted_data:
                print("下载头像图片...")
                extracted_data = await self.download_head_images(
                    extracted_data['head_image_list'], kuaidong_id, extracted_data, directories
                )

            # 提取社交账号信息
            social_ids = {}
            if 'reference_list' in extracted_data:
                print("提取社交账号信息...")
                social_ids = await self.extract_social_ids(extracted_data['reference_list'])
                # 将社交账号信息添加到提取数据中
                extracted_data['social_ids'] = social_ids

            # 下载视频
            if 'video_list' in extracted_data:
                print("下载视频...")
                extracted_data = await self.download_videos(
                    extracted_data['video_list'], kuaidong_id, extracted_data, directories
                )

            # 重新保存更新后的数据（包含所有新字段）
            filename = f"{kuaidong_id}.saved.json"
            filepath = directories['json_dir'] / filename

            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(extracted_data, ensure_ascii=False, indent=2))

            # 构建返回结果
            result = {
                'kuaidong_id': kuaidong_id,
                'name': actor_name,
                'url': url,
                'scraped_at': datetime.now().isoformat(),
                'html_path': html_path,
                'origin_json_path': origin_path,
                'saved_json_path': str(filepath),
                'extracted_fields': list(extracted_data.keys()),
                'social_ids': social_ids,
                'stats': {
                    'head_images_count': len(extracted_data.get('downloaded_head_images', [])),
                    'videos_count': len(extracted_data.get('downloaded_videos', [])),
                    'social_platforms': len(social_ids),
                    'total_fields': len(extracted_data)
                }
            }

            print(f"演员 {actor_name} 信息提取完成")
            print(f"提取字段: {len(extracted_data)} 个")
            print(f"下载头像: {result['stats']['head_images_count']} 张")
            print(f"下载视频: {result['stats']['videos_count']} 个")
            print(f"社交平台: {result['stats']['social_platforms']} 个")

            return result

        except Exception as e:
            print(f"爬取演员 {actor_name} 失败: {e}")
            return {
                'kuaidong_id': kuaidong_id,
                'name': actor_name,
                'url': url,
                'error': str(e),
                'scraped_at': datetime.now().isoformat()
            }
        finally:
            await page.close()

    async def load_actors_data(self, json_file_path: str) -> List[Dict]:
        """加载演员数据文件"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                actors_data = json.load(f)
            print(f"成功加载演员数据，共 {len(actors_data)} 个演员")
            return actors_data
        except Exception as e:
            print(f"加载演员数据失败: {e}")
            return []

    async def batch_scrape_actors(self, json_file_path: str, start_index: int = 0, max_count: Optional[int] = None):
        """批量爬取演员数据"""
        # 加载演员数据
        actors_data = await self.load_actors_data(json_file_path)
        if not actors_data:
            return

        # 确定处理范围
        total_actors = len(actors_data)
        end_index = min(start_index + max_count, total_actors) if max_count else total_actors

        print(f"开始批量爬取演员数据")
        print(f"总演员数: {total_actors}")
        print(f"处理范围: {start_index} - {end_index-1}")
        print(f"本次处理: {end_index - start_index} 个演员")

        # 统计信息
        success_count = 0
        error_count = 0
        results = []

        for i in range(start_index, end_index):
            actor = actors_data[i]
            kuaidong_id = actor.get('kuaidong_id')
            actor_name = actor.get('name', f'Actor_{i}')

            if not kuaidong_id:
                print(f"跳过演员 {actor_name}: 没有 kuaidong_id")
                error_count += 1
                continue

            print(f"\n{'='*60}")
            print(f"处理进度: {i+1-start_index}/{end_index-start_index}")
            print(f"演员: {actor_name} (ID: {kuaidong_id})")
            print(f"{'='*60}")

            try:
                # 爬取单个演员数据
                result = await self.scrape_actor_detail(kuaidong_id, actor_name)

                if 'error' not in result:
                    success_count += 1
                    print(f"✅ 成功处理演员: {actor_name}")

                    # 显示统计信息
                    stats = result.get('stats', {})
                    print(f"   提取字段: {stats.get('total_fields', 0)} 个")
                    print(f"   下载头像: {stats.get('head_images_count', 0)} 张")
                    print(f"   下载视频: {stats.get('videos_count', 0)} 个")
                    print(f"   社交平台: {stats.get('social_platforms', 0)} 个")
                else:
                    error_count += 1
                    print(f"❌ 处理失败演员: {actor_name}")
                    print(f"   错误信息: {result.get('error', 'Unknown error')}")

                # 添加原始演员信息到结果中
                result['original_actor_data'] = actor
                results.append(result)

                # 每处理5个演员暂停一下，避免请求过于频繁
                if (i + 1) % 5 == 0:
                    print(f"\n⏸️  已处理 {i+1-start_index} 个演员，暂停 3 秒...")
                    await asyncio.sleep(3)

            except Exception as e:
                error_count += 1
                print(f"❌ 处理演员 {actor_name} 时发生异常: {e}")
                results.append({
                    'kuaidong_id': kuaidong_id,
                    'name': actor_name,
                    'error': str(e),
                    'original_actor_data': actor
                })

        # 保存批量处理结果
        batch_result = {
            'batch_info': {
                'total_actors': total_actors,
                'start_index': start_index,
                'end_index': end_index,
                'processed_count': end_index - start_index,
                'success_count': success_count,
                'error_count': error_count,
                'success_rate': f"{success_count/(end_index-start_index)*100:.1f}%" if end_index > start_index else "0%",
                'processed_at': datetime.now().isoformat()
            },
            'results': results
        }

        # 保存批量结果文件
        batch_filename = f"batch_result_{start_index}_{end_index-1}.json"
        with open(batch_filename, 'w', encoding='utf-8') as f:
            json.dump(batch_result, f, ensure_ascii=False, indent=2)

        print(f"\n{'='*60}")
        print(f"🎉 批量处理完成!")
        print(f"{'='*60}")
        print(f"总处理数: {end_index - start_index}")
        print(f"成功数: {success_count}")
        print(f"失败数: {error_count}")
        print(f"成功率: {success_count/(end_index-start_index)*100:.1f}%")
        print(f"批量结果保存到: {batch_filename}")
        print(f"{'='*60}")


async def main():
    """主函数 - 支持单个测试和批量处理"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1]

        if mode == "batch":
            # 批量处理模式
            start_index = int(sys.argv[2]) if len(sys.argv) > 2 else 0
            max_count = int(sys.argv[3]) if len(sys.argv) > 3 else None

            async with KuaidongDataExtractor() as extractor:
                await extractor.batch_scrape_actors(
                    "duanju_actors_with_kuaidong.json",
                    start_index=start_index,
                    max_count=max_count
                )
        elif mode == "test":
            # 单个测试模式
            test_kuaidong_id = sys.argv[2] if len(sys.argv) > 2 else "7387251624085438474"
            test_actor_name = sys.argv[3] if len(sys.argv) > 3 else "王小亿"

            async with KuaidongDataExtractor() as extractor:
                result = await extractor.scrape_actor_detail(test_kuaidong_id, test_actor_name)

                # 保存测试结果
                with open("kuaidong_extraction_result.json", "w", encoding="utf-8") as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)

                print(f"\n=== 数据提取完成 ===")
                print(f"结果保存到: kuaidong_extraction_result.json")

                if 'error' not in result:
                    print(f"\n=== 提取统计 ===")
                    stats = result.get('stats', {})
                    print(f"提取字段数: {stats.get('total_fields', 0)}")
                    print(f"下载头像数: {stats.get('head_images_count', 0)}")
                    print(f"下载视频数: {stats.get('videos_count', 0)}")
                    print(f"社交平台数: {stats.get('social_platforms', 0)}")

                    print(f"\n=== 文件路径 ===")
                    print(f"HTML文件: {result.get('html_path', 'N/A')}")
                    print(f"原始JSON: {result.get('origin_json_path', 'N/A')}")
                    print(f"处理JSON: {result.get('saved_json_path', 'N/A')}")
                else:
                    print(f"提取失败: {result.get('error', 'Unknown error')}")
        else:
            print("未知模式，请使用 'batch' 或 'test'")
            print("使用方法:")
            print("  python kuaidong_data_extractor.py batch [start_index] [max_count]")
            print("  python kuaidong_data_extractor.py test [kuaidong_id] [actor_name]")
    else:
        # 默认批量处理前3个演员
        print("默认模式：批量处理前3个演员")
        print("使用方法:")
        print("  python kuaidong_data_extractor.py batch [start_index] [max_count]")
        print("  python kuaidong_data_extractor.py test [kuaidong_id] [actor_name]")
        print()

        async with KuaidongDataExtractor() as extractor:
            await extractor.batch_scrape_actors(
                "duanju_actors_with_kuaidong.json",
                start_index=0,
                max_count=3
            )


if __name__ == "__main__":
    asyncio.run(main())
