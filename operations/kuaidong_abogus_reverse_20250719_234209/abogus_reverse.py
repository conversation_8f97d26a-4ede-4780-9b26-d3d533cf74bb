#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快懂百科 a_bogus 参数逆向实现
基于对 bdms.js 的分析，实现 a_bogus 参数生成算法
"""

import hashlib
import hmac
import time
import random
import string
import base64
import json
from urllib.parse import urlencode, quote, urlparse
import struct

class ABogusGenerator:
    """a_bogus 参数生成器"""
    
    def __init__(self):
        # 基于分析发现的常量和模式
        self.magic_constants = [
            0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476,  # MD5 常量
            0x5A827999, 0x6ED9EBA1, 0x8F1BBCDC, 0xCA62C1D6   # SHA1 常量
        ]
        
        # 字符集
        self.charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        self.url_safe_charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
        
        # 从 bdms.js 中提取的可能密钥
        self.possible_keys = [
            "484e4f4a403f524300071629009a8af00000002a99f1a33e",
            "mhe",
            "bdms"
        ]
    
    def get_timestamp(self):
        """获取时间戳"""
        return int(time.time() * 1000)
    
    def get_random_string(self, length=8):
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def custom_hash(self, data, algorithm='md5'):
        """自定义哈希函数"""
        if algorithm == 'md5':
            return hashlib.md5(data.encode() if isinstance(data, str) else data).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(data.encode() if isinstance(data, str) else data).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(data.encode() if isinstance(data, str) else data).hexdigest()
    
    def custom_base64_encode(self, data):
        """自定义 Base64 编码"""
        if isinstance(data, str):
            data = data.encode()
        return base64.b64encode(data).decode().rstrip('=')
    
    def url_safe_base64_encode(self, data):
        """URL 安全的 Base64 编码"""
        if isinstance(data, str):
            data = data.encode()
        return base64.urlsafe_b64encode(data).decode().rstrip('=')
    
    def generate_signature_v1(self, url, data=None, timestamp=None):
        """方法1: 基于 URL 和数据的简单签名"""
        if timestamp is None:
            timestamp = self.get_timestamp()
        
        # 构建签名字符串
        sign_str = f"{url}"
        if data:
            if isinstance(data, dict):
                sign_str += json.dumps(data, separators=(',', ':'), sort_keys=True)
            else:
                sign_str += str(data)
        sign_str += str(timestamp)
        
        # 生成哈希
        hash_value = self.custom_hash(sign_str, 'md5')
        
        # Base64 编码并截取
        result = self.custom_base64_encode(hash_value)[:32]
        return result
    
    def generate_signature_v2(self, url, data=None, timestamp=None):
        """方法2: 使用 HMAC 的签名"""
        if timestamp is None:
            timestamp = self.get_timestamp()
        
        # 构建消息
        message = f"{url}|{timestamp}"
        if data:
            if isinstance(data, dict):
                message += "|" + json.dumps(data, separators=(',', ':'), sort_keys=True)
            else:
                message += "|" + str(data)
        
        # 使用可能的密钥生成 HMAC
        for key in self.possible_keys:
            signature = hmac.new(
                key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # 转换为 Base64 并截取
            result = self.custom_base64_encode(signature)[:32]
            if self.is_valid_abogus_format(result):
                return result
        
        # 如果没有找到合适的密钥，使用默认方法
        return self.custom_base64_encode(hashlib.sha256(message.encode()).hexdigest())[:32]
    
    def generate_signature_v3(self, url, data=None, timestamp=None):
        """方法3: 模拟字节跳动的算法"""
        if timestamp is None:
            timestamp = self.get_timestamp()
        
        # 解析 URL
        parsed_url = urlparse(url)
        
        # 构建基础字符串
        base_parts = [
            parsed_url.path,
            str(timestamp),
            self.get_random_string(4)
        ]
        
        if data:
            if isinstance(data, dict):
                # 对参数进行特殊处理
                data_str = json.dumps(data, separators=(',', ':'))
                base_parts.append(hashlib.md5(data_str.encode()).hexdigest()[:8])
            else:
                base_parts.append(str(data))
        
        # 组合字符串
        combined = '|'.join(base_parts)
        
        # 多轮哈希
        hash1 = hashlib.md5(combined.encode()).hexdigest()
        hash2 = hashlib.sha1((hash1 + str(timestamp)).encode()).hexdigest()
        
        # 自定义编码
        result = self.custom_encode(hash2)
        return result[:32]
    
    def custom_encode(self, data):
        """自定义编码算法"""
        # 模拟可能的编码逻辑
        encoded = ""
        for i, char in enumerate(data):
            # 使用字符的 ASCII 值和位置进行变换
            ascii_val = ord(char)
            transformed = (ascii_val + i) % len(self.url_safe_charset)
            encoded += self.url_safe_charset[transformed]
        
        return encoded
    
    def is_valid_abogus_format(self, value):
        """检查是否符合 a_bogus 格式"""
        # a_bogus 通常是 32 位长度的字符串，包含字母和数字
        if len(value) != 32:
            return False
        
        # 检查字符集
        valid_chars = set(string.ascii_letters + string.digits + '+/-_=')
        return all(c in valid_chars for c in value)
    
    def generate_abogus(self, url, data=None, method='v3'):
        """生成 a_bogus 参数"""
        timestamp = self.get_timestamp()
        
        if method == 'v1':
            result = self.generate_signature_v1(url, data, timestamp)
        elif method == 'v2':
            result = self.generate_signature_v2(url, data, timestamp)
        elif method == 'v3':
            result = self.generate_signature_v3(url, data, timestamp)
        else:
            # 默认使用 v3
            result = self.generate_signature_v3(url, data, timestamp)
        
        return {
            'a_bogus': result,
            'timestamp': timestamp,
            'method': method
        }
    
    def test_all_methods(self, url, data=None):
        """测试所有方法"""
        results = {}
        for method in ['v1', 'v2', 'v3']:
            try:
                result = self.generate_abogus(url, data, method)
                results[method] = result
            except Exception as e:
                results[method] = {'error': str(e)}
        
        return results

def main():
    """主函数 - 测试 a_bogus 生成"""
    print("🔐 快懂百科 a_bogus 参数生成器")
    print("=" * 50)
    
    # 初始化生成器
    generator = ABogusGenerator()
    
    # 测试数据
    test_url = "https://www.baike.com/api/v2/search/getDocSugDataV2"
    test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
    
    print(f"🎯 测试 URL: {test_url}")
    print(f"📦 测试数据: {test_data}")
    print()
    
    # 测试所有方法
    results = generator.test_all_methods(test_url, test_data)
    
    print("📊 生成结果:")
    for method, result in results.items():
        print(f"\n🔹 方法 {method.upper()}:")
        if 'error' in result:
            print(f"   ❌ 错误: {result['error']}")
        else:
            print(f"   ✅ a_bogus: {result['a_bogus']}")
            print(f"   🕐 时间戳: {result['timestamp']}")
    
    # 保存结果
    output_file = "operations/kuaidong_abogus_reverse_20250719_234209/abogus_test_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: {output_file}")
    
    # 生成测试用的 curl 命令
    print("\n🧪 测试建议:")
    print("1. 使用生成的 a_bogus 值替换原始请求中的参数")
    print("2. 观察服务器响应，验证参数是否有效")
    print("3. 如果无效，尝试调整算法参数")
    
    # 示例 curl 命令
    example_abogus = results.get('v3', {}).get('a_bogus', 'EXAMPLE_VALUE')
    print(f"\n📋 示例 curl 命令:")
    print(f"""curl '{test_url}?a_bogus={example_abogus}' \\
  -H 'Content-Type: application/json' \\
  --data-raw '{json.dumps(test_data)}'""")

if __name__ == "__main__":
    main()
