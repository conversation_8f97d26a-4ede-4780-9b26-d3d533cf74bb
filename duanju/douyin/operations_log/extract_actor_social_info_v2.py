#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员社交媒体信息提取脚本 V2
从谷歌搜索结果中提取演员的社交媒体账号信息
使用火山引擎大模型API

提取内容：
1. 小红书账号 ID
2. 微博 ID
3. 微博超话 ID
4. 短剧演员的介绍
5. 抖音 ID

作者: AI Assistant
创建时间: 2025-07-19
版本: V2 - 使用火山引擎API
"""

import json
import re
import urllib.parse
import requests
from typing import Dict, List, Optional, Any
import time
import os

class ActorSocialInfoExtractorV2:
    def __init__(self):
        self.api_key = "a3a4d362-1b3e-4fd9-9a08-7ac453846ed0"
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.model = "ep-20250213164205-z5x62"
        
    def load_search_results(self, file_path: str) -> Dict:
        """加载搜索结果文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_actors_list(self, file_path: str) -> List[Dict]:
        """加载需要处理的演员列表"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def extract_weibo_id(self, url: str) -> Optional[str]:
        """从微博链接中提取微博ID"""
        # 微博 id 示例：多来自于 links 中的 "https://m.weibo.cn/n/%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF" 的 %E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF 注意需要转义
        pattern = r'https://m\.weibo\.cn/n/([^/?]+)'
        match = re.search(pattern, url)
        if match:
            encoded_id = match.group(1)
            try:
                # URL解码
                decoded_id = urllib.parse.unquote(encoded_id)
                return decoded_id
            except:
                return encoded_id
        return None
    
    def extract_weibo_super_topic_id(self, url: str) -> Optional[str]:
        """从微博超话链接中提取超话ID"""
        # 微博超话 id 示例 https://m.weibo.cn/p/index?extparam=%E6%BC%94%E5%91%98%E7%8E%8B%E5%B0%8F%E4%BA%BF&containerid=1008083a3b0dce4bca7313f49dbebb5f983cae&luicode=10000011&lfid=1005057812743270&featurecode=newtitle中的 containerid=1008083a3b0dce4bca7313f49dbebb5f983cae
        pattern = r'containerid=([^&]+)'
        match = re.search(pattern, url)
        if match:
            return match.group(1)
        return None
    
    def extract_douyin_id(self, url: str) -> Optional[str]:
        """从抖音链接中提取抖音ID"""
        # 抖音账号一般有 https://www.douyin.com/user/ 前缀
        pattern = r'https://www\.douyin\.com/user/([^/?]+)'
        match = re.search(pattern, url)
        if match:
            return match.group(1)
        return None
    
    def extract_xiaohongshu_id(self, url: str) -> Optional[str]:
        """从小红书链接中提取小红书ID"""
        # 小红书链接模式
        patterns = [
            r'https://www\.xiaohongshu\.com/user/profile/([^/?]+)',
            r'https://www\.xiaohongshu\.com/user/([^/?]+)',
            r'https://xhslink\.com/([^/?]+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def call_llm_api(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """调用火山引擎的LLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的信息提取助手，擅长从网页搜索结果中提取演员的社交媒体账号信息。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_url, headers=headers, json=data, timeout=30)
                response.raise_for_status()
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    print(f"API响应格式异常: {result}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                print(f"LLM API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    return None
            except Exception as e:
                print(f"LLM API调用异常: {e}")
                return None
    
    def extract_description_from_metadata(self, metadata: Dict) -> Optional[str]:
        """从metadata中提取描述信息"""
        # 简介介绍多来自于百度百科或者维基百科类网站的 metadata 中，比如ogDescription 等等
        description_fields = ['ogDescription', 'description', 'og:description']
        for field in description_fields:
            if field in metadata and metadata[field]:
                return metadata[field]
        return None
    
    def process_search_result(self, actor_name: str, search_data: List[Dict]) -> Dict:
        """处理单个演员的搜索结果"""
        result = {
            "actor_name": actor_name,
            "xiaohongshu_id": None,
            "weibo_id": None,
            "weibo_super_topic_id": None,
            "description": None,
            "douyin_id": None,
            "extracted_links": [],
            "llm_analysis": None
        }
        
        # 收集所有相关链接和描述
        all_links = []
        descriptions = []
        
        for item in search_data:
            # 提取链接
            if 'links' in item:
                all_links.extend(item['links'])
            if 'url' in item:
                all_links.append(item['url'])
            
            # 提取描述
            if 'description' in item and item['description']:
                descriptions.append(item['description'])
            
            if 'metadata' in item:
                desc = self.extract_description_from_metadata(item['metadata'])
                if desc:
                    descriptions.append(desc)
        
        # 从链接中直接提取ID
        for link in all_links:
            # 微博ID
            weibo_id = self.extract_weibo_id(link)
            if weibo_id and not result["weibo_id"]:
                result["weibo_id"] = weibo_id
            
            # 微博超话ID
            super_topic_id = self.extract_weibo_super_topic_id(link)
            if super_topic_id and not result["weibo_super_topic_id"]:
                result["weibo_super_topic_id"] = super_topic_id
            
            # 抖音ID
            douyin_id = self.extract_douyin_id(link)
            if douyin_id and not result["douyin_id"]:
                result["douyin_id"] = douyin_id
            
            # 小红书ID
            xhs_id = self.extract_xiaohongshu_id(link)
            if xhs_id and not result["xiaohongshu_id"]:
                result["xiaohongshu_id"] = xhs_id
        
        result["extracted_links"] = list(set(all_links))
        
        # 选择最佳描述
        if descriptions:
            # 优先选择百度百科的描述
            baike_desc = None
            for desc in descriptions:
                if len(desc) > 50 and ("演员" in desc or "出生" in desc or "毕业" in desc):
                    baike_desc = desc
                    break
            
            result["description"] = baike_desc or descriptions[0]
        
        return result
    
    def enhance_with_llm(self, actor_name: str, search_data: List[Dict], initial_result: Dict) -> Dict:
        """使用LLM增强提取结果"""
        # 构建LLM提示
        prompt = f"""
请分析以下关于演员"{actor_name}"的搜索结果，提取以下信息：

1. 小红书账号ID（从小红书相关链接中提取）
2. 微博ID（从微博链接中提取，需要URL解码）
3. 微博超话ID（从微博超话链接的containerid参数中提取）
4. 演员简介（优先选择百度百科等权威来源的描述）
5. 抖音ID（从抖音链接中提取）

搜索结果数据（前3000字符）：
{json.dumps(search_data, ensure_ascii=False, indent=2)[:3000]}

当前已提取的结果：
{json.dumps(initial_result, ensure_ascii=False, indent=2)}

请以JSON格式返回补充或修正的信息，格式如下：
{{
    "xiaohongshu_id": "小红书ID或null",
    "weibo_id": "微博ID或null", 
    "weibo_super_topic_id": "微博超话ID或null",
    "description": "演员简介或null",
    "douyin_id": "抖音ID或null",
    "confidence": "提取置信度(1-10)",
    "notes": "提取说明"
}}
"""
        
        llm_response = self.call_llm_api(prompt)
        if llm_response:
            try:
                # 尝试解析LLM返回的JSON
                llm_result = json.loads(llm_response)
                
                # 合并LLM结果到初始结果
                for key in ["xiaohongshu_id", "weibo_id", "weibo_super_topic_id", "description", "douyin_id"]:
                    if key in llm_result and llm_result[key] and not initial_result[key]:
                        initial_result[key] = llm_result[key]
                
                initial_result["llm_analysis"] = llm_result
                
            except json.JSONDecodeError:
                initial_result["llm_analysis"] = {"raw_response": llm_response}
        
        return initial_result
    
    def process_all_actors(self, search_results_file: str, actors_file: str, output_file: str):
        """处理所有演员的搜索结果"""
        # 加载数据
        search_results = self.load_search_results(search_results_file)
        actors_list = self.load_actors_list(actors_file)
        
        # 创建演员名称到ID的映射
        actor_name_to_id = {actor['name']: actor['id'] for actor in actors_list}
        
        results = []
        processed_count = 0
        
        for actor_id, search_data in search_results.items():
            if not search_data.get('success', False):
                continue
            
            # 找到对应的演员名称
            actor_name = None
            for actor in actors_list:
                if actor['id'] == actor_id:
                    actor_name = actor['name']
                    break
            
            if not actor_name:
                continue
            
            print(f"处理演员: {actor_name} (ID: {actor_id})")
            
            # 处理搜索结果
            initial_result = self.process_search_result(actor_name, search_data['data'])
            
            # 使用LLM增强结果
            enhanced_result = self.enhance_with_llm(actor_name, search_data['data'], initial_result)
            enhanced_result['actor_id'] = actor_id
            
            results.append(enhanced_result)
            processed_count += 1
            
            # 添加延迟避免API限制
            time.sleep(2)
            
            print(f"已处理 {processed_count} 个演员")
            
            # 每处理10个演员保存一次中间结果
            if processed_count % 10 == 0:
                temp_file = f"operations_log/temp_results_{processed_count}.json"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"中间结果已保存到: {temp_file}")
        
        # 保存最终结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"处理完成，共处理 {processed_count} 个演员，结果保存到: {output_file}")
        
        return results

def main():
    """主函数"""
    # 创建操作记录目录
    os.makedirs("operations_log", exist_ok=True)
    
    extractor = ActorSocialInfoExtractorV2()
    
    # 文件路径
    search_results_file = "search_user_results.json"
    actors_file = "duanju_actors_need_scrape.json"
    output_file = "operations_log/extracted_social_info_v2.json"
    
    # 处理所有演员
    results = extractor.process_all_actors(search_results_file, actors_file, output_file)
    
    # 生成统计报告
    stats = {
        "total_processed": len(results),
        "xiaohongshu_found": sum(1 for r in results if r.get('xiaohongshu_id')),
        "weibo_found": sum(1 for r in results if r.get('weibo_id')),
        "weibo_super_topic_found": sum(1 for r in results if r.get('weibo_super_topic_id')),
        "description_found": sum(1 for r in results if r.get('description')),
        "douyin_found": sum(1 for r in results if r.get('douyin_id'))
    }
    
    print("\n=== 提取统计 ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # 保存统计报告
    with open("operations_log/extraction_stats_v2.json", 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    main()
