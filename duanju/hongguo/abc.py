import frida, sys

def on_message(message, data):
    if message['type'] == 'send':
        print("[*] {0}".format(message['payload']))
    else:
        print(message)

jscode = """
// script.js

// 查找 OpenSSL 中的 SSL_CTX_set_min_proto_version 函数地址
const sslCtxSetMinProtoVersionAddr = Module.findExportByName(null, "SSL_CTX_set_min_proto_version");

if (sslCtxSetMinProtoVersionAddr) {
    console.log("[*] Found SSL_CTX_set_min_proto_version at:", sslCtxSetMinProtoVersionAddr);

    // 使用 Interceptor 挂钩该函数
    Interceptor.attach(sslCtxSetMinProtoVersionAddr, {
        onEnter: function (args) {
            // args[0]: SSL_CTX *ctx
            // args[1]: int version （传入的最低协议版本）

            console.log("[*] SSL_CTX_set_min_proto_version called");
            console.log("    Original version:", args[1]);

            // 强制将最低协议版本设置为 TLS 1.0 （OpenSSL 中定义为 0x0301）
            const TLS1_VERSION = 0x0301; // TLS 1.0
            args[1] = TLS1_VERSION;

            console.log("    Forced version:", args[1]);
        },
        onLeave: function (retval) {
            console.log("[*] SSL_CTX_set_min_proto_version returned:", retval);
        }
    });
} else {
    console.error("[!] SSL_CTX_set_min_proto_version not found. The target may not use OpenSSL, or it's statically linked.");
}
"""


process = frida.get_usb_device().attach('com.phoenix.read:sandboxed_process0')
script = process.create_script(jscode)
script.on('message', on_message)
print('[*] Running CTF')
script.load()
sys.stdin.read()