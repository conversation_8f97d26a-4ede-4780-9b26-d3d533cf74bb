#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快懂百科 a_bogus 参数动态测试脚本
用于验证生成的 a_bogus 参数是否有效
"""

import requests
import json
import time
import random
from abogus_reverse import ABogusGenerator

class ABogusValidator:
    """a_bogus 参数验证器"""
    
    def __init__(self):
        self.generator = ABogusGenerator()
        self.session = requests.Session()
        
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Origin': 'https://www.baike.com',
            'Referer': 'https://www.baike.com/',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
    
    def test_abogus_parameter(self, url, data, abogus_value, mstoken=None):
        """测试 a_bogus 参数是否有效"""
        try:
            # 构建完整的 URL
            params = {'a_bogus': abogus_value}
            if mstoken:
                params['msToken'] = mstoken
            
            # 发送请求
            response = self.session.post(
                url,
                params=params,
                json=data,
                timeout=10
            )
            
            # 分析响应
            result = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response_size': len(response.content),
                'headers': dict(response.headers),
                'url': response.url
            }
            
            # 尝试解析 JSON 响应
            try:
                json_response = response.json()
                result['json_response'] = json_response
                result['has_data'] = 'data' in json_response or 'result' in json_response
            except:
                result['text_response'] = response.text[:500]  # 只保存前500字符
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_mstoken(self):
        """生成 msToken 参数"""
        # msToken 通常是一个随机字符串
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'
        return ''.join(random.choices(chars, k=48))
    
    def comprehensive_test(self, url, data):
        """综合测试所有生成方法"""
        print(f"🧪 开始综合测试...")
        print(f"URL: {url}")
        print(f"数据: {data}")
        print()
        
        results = {}
        
        # 测试所有生成方法
        for method in ['v1', 'v2', 'v3']:
            print(f"🔹 测试方法 {method.upper()}...")
            
            # 生成 a_bogus
            abogus_result = self.generator.generate_abogus(url, data, method)
            abogus_value = abogus_result['a_bogus']
            
            # 生成 msToken
            mstoken = self.generate_mstoken()
            
            print(f"   a_bogus: {abogus_value}")
            print(f"   msToken: {mstoken}")
            
            # 测试请求
            test_result = self.test_abogus_parameter(url, data, abogus_value, mstoken)
            
            # 记录结果
            results[method] = {
                'abogus_params': abogus_result,
                'mstoken': mstoken,
                'test_result': test_result
            }
            
            # 输出测试结果
            if test_result.get('success'):
                print(f"   ✅ 请求成功 (状态码: {test_result['status_code']})")
                if test_result.get('has_data'):
                    print(f"   📊 响应包含数据")
                else:
                    print(f"   📝 响应: {test_result.get('text_response', '')[:100]}...")
            else:
                print(f"   ❌ 请求失败: {test_result.get('error', '状态码: ' + str(test_result.get('status_code', 'Unknown')))}")
            
            print()
            time.sleep(1)  # 避免请求过快
        
        return results
    
    def analyze_patterns(self, results):
        """分析测试结果中的模式"""
        print("📊 分析测试结果...")
        
        successful_methods = []
        failed_methods = []
        
        for method, result in results.items():
            if result['test_result'].get('success'):
                successful_methods.append(method)
            else:
                failed_methods.append(method)
        
        print(f"✅ 成功的方法: {successful_methods}")
        print(f"❌ 失败的方法: {failed_methods}")
        
        # 分析响应模式
        if successful_methods:
            print("\n🔍 成功响应分析:")
            for method in successful_methods:
                result = results[method]['test_result']
                print(f"   方法 {method.upper()}:")
                print(f"     状态码: {result['status_code']}")
                print(f"     响应大小: {result['response_size']} 字节")
                if 'json_response' in result:
                    print(f"     JSON 响应: {json.dumps(result['json_response'], ensure_ascii=False)[:200]}...")
        
        return {
            'successful_methods': successful_methods,
            'failed_methods': failed_methods,
            'analysis': "基于测试结果的分析"
        }
    
    def save_results(self, results, analysis):
        """保存测试结果"""
        output_data = {
            'timestamp': time.time(),
            'test_results': results,
            'analysis': analysis
        }
        
        output_file = "validation_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试结果已保存到: {output_file}")

def main():
    """主函数"""
    print("🚀 快懂百科 a_bogus 参数动态验证")
    print("=" * 50)
    
    # 初始化验证器
    validator = ABogusValidator()
    
    # 测试参数
    test_url = "https://www.baike.com/api/v2/search/getDocSugDataV2"
    test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
    
    # 执行综合测试
    results = validator.comprehensive_test(test_url, test_data)
    
    # 分析结果
    analysis = validator.analyze_patterns(results)
    
    # 保存结果
    validator.save_results(results, analysis)
    
    print("\n📋 总结:")
    print("1. 如果有方法成功，说明算法方向正确")
    print("2. 如果都失败，可能需要更深入的逆向分析")
    print("3. 观察响应内容，寻找错误信息中的线索")
    print("4. 考虑其他参数（如 Cookie、时间戳等）的影响")
    
    # 生成进一步分析的建议
    if analysis['successful_methods']:
        print(f"\n🎉 发现有效方法: {', '.join(analysis['successful_methods'])}")
        print("建议：基于成功的方法进一步优化算法")
    else:
        print("\n🔧 所有方法都失败，建议：")
        print("1. 检查请求头是否完整")
        print("2. 分析是否需要其他参数（如 Cookie）")
        print("3. 使用浏览器开发者工具对比真实请求")
        print("4. 考虑参数的时效性和顺序")

if __name__ == "__main__":
    main()
