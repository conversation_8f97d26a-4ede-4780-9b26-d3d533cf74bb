import { chromium } from 'playwright';
import logger from './logger.js';

export class BaikeSearcher {
  constructor(options = {}) {
    this.options = {
      headless: options.headless !== false, // 默认无头模式
      timeout: options.timeout || 30000,    // 30秒超时
      ...options
    };
    this.browser = null;
    this.page = null;
  }

  async init() {
    try {
      console.log('🚀 启动浏览器...');
      this.browser = await chromium.launch({
        headless: this.options.headless,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      this.page = await this.browser.newPage();

      // 设置用户代理
      await this.page.setExtraHTTPHeaders({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      });
      
      console.log('✅ 浏览器启动成功');
    } catch (error) {
      logger.logError('browser_init', error);
      throw new Error(`浏览器启动失败: ${error.message}`);
    }
  }

  async navigateToSite() {
    try {
      console.log('🌐 导航到快懂百科...');
      await this.page.goto('https://www.baike.com/', {
        waitUntil: 'networkidle',
        timeout: this.options.timeout
      });

      console.log('⏳ 等待所有 JavaScript 加载完毕...');

      // 等待页面完全加载
      await this.page.waitForLoadState('domcontentloaded');
      await this.page.waitForLoadState('networkidle');

      // 等待一段时间确保所有异步脚本执行完毕
      await this.page.waitForTimeout(3000);

      // 等待搜索框加载 - 使用更准确的选择器
      await this.page.waitForSelector('input[placeholder*="搜索"]', { timeout: 10000 });

      // 再等待一下确保搜索相关的 JS 完全加载
      await this.page.waitForTimeout(2000);

      console.log('✅ 页面和所有 JavaScript 加载完成');
    } catch (error) {
      logger.logError('navigation', error);
      throw new Error(`页面导航失败: ${error.message}`);
    }
  }

  async interceptApiRequest(keyword) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('API请求拦截超时'));
      }, this.options.timeout);

      // 启用请求拦截
      this.page.route('**/*', async (route, request) => {
        const url = request.url();

        // 检查是否是目标API请求
        if (url.includes('/api/v2/search/getDocSugDataV2') ||
            (url.includes('getDocSugDataV2') && url.includes('baike.com'))) {

          console.log('� 拦截到目标API请求:', request.method(), url);

          // 解析URL参数
          const urlObj = new URL(url);
          const msToken = urlObj.searchParams.get('msToken');
          const aBogus = urlObj.searchParams.get('a_bogus');

          console.log('📋 提取的参数:');
          console.log('   msToken:', msToken);
          console.log('   a_bogus:', aBogus);

          // 如果是POST请求，也记录请求体
          if (request.method() === 'POST') {
            const postData = request.postData();
            console.log('📤 POST请求体:', postData);
          }

          // 记录到日志
          const logData = {
            url,
            msToken,
            aBogus,
            method: request.method(),
            postData: request.method() === 'POST' ? request.postData() : null,
            keyword,
            timestamp: new Date().toISOString()
          };

          logger.log('api_intercept', logData);

          console.log('🚫 阻止请求发出，直接返回结果');

          clearTimeout(timeout);

          // 返回拦截到的参数
          resolve({
            url,
            msToken,
            aBogus,
            method: request.method(),
            postData: request.method() === 'POST' ? request.postData() : null,
            intercepted: true
          });

          // 阻止请求发出
          route.abort();
          return;
        }

        // 其他请求正常继续
        route.continue();
      });
    });
  }

  extractSearchResults(responseData) {
    try {
      // 根据API响应结构提取数据
      if (responseData && responseData.data && responseData.data.list) {
        return responseData.data.list.map(item => ({
          DocID: item.DocID || item.docId,
          ID: item.ID || item.id || item.DocID || item.docId,
          Subtitle: item.Subtitle || item.subtitle || '',
          Title: item.Title || item.title || ''
        }));
      }
      
      // 如果结构不同，尝试其他可能的结构
      if (responseData && responseData.list) {
        return responseData.list.map(item => ({
          DocID: item.DocID || item.docId,
          ID: item.ID || item.id || item.DocID || item.docId,
          Subtitle: item.Subtitle || item.subtitle || '',
          Title: item.Title || item.title || ''
        }));
      }

      return [];
    } catch (error) {
      logger.logError('extract_results', error, { responseData });
      return [];
    }
  }

  async search(keyword) {
    try {
      if (!this.browser) {
        await this.init();
      }

      await this.navigateToSite();

      console.log(`🔍 搜索关键词: ${keyword}`);

      // 设置API请求拦截
      const apiInterceptPromise = this.interceptApiRequest(keyword);

      // 找到搜索框并输入关键词
      const searchInput = this.page.locator('input[placeholder*="搜索"]').first();

      // 先点击搜索框以确保焦点
      await searchInput.click();
      await this.page.waitForTimeout(500);

      // 清空并输入关键词
      await searchInput.fill('');
      await this.page.waitForTimeout(200);
      await searchInput.pressSequentially(keyword, { delay: 150 });

      // 等待一下让API请求触发
      await this.page.waitForTimeout(1000);

      // 等待API请求被拦截
      const interceptResult = await apiInterceptPromise;

      console.log(`✅ 成功拦截API请求并提取参数`);
      console.log(`📋 msToken: ${interceptResult.msToken}`);
      console.log(`📋 a_bogus: ${interceptResult.aBogus}`);

      return interceptResult;

    } catch (error) {
      logger.logError('search', error, { keyword });
      throw error;
    }
  }

  async close() {
    try {
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
        this.page = null;
        console.log('🔒 浏览器已关闭');
      }
    } catch (error) {
      logger.logError('browser_close', error);
    }
  }



  // 批量搜索多个关键词
  async batchSearch(keywords) {
    const results = {};

    for (const keyword of keywords) {
      try {
        console.log(`\n--- 搜索: ${keyword} ---`);
        results[keyword] = await this.search(keyword);

        // 短暂延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`❌ 搜索 "${keyword}" 失败:`, error.message);
        results[keyword] = null;
      }
    }

    return results;
  }
}
