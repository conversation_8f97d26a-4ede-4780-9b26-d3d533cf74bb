"""
52kanduanju.com 主爬虫脚本
"""

import os
import sys
import requests
import logging
from typing import List, Set
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import (
    MAIN_PAGE_URL, CATEGORIES, CATEGORY_URL_TEMPLATE, 
    DATA_DIR, DRAMAS_FILE, ACTORS_FILE, CATEGORIES_FILE, LOG_FILE,
    LOG_LEVEL
)
from models import DataManager, Drama, Actor, Category
from utils import (
    setup_logging, make_request, parse_drama_list_from_html,
    parse_drama_detail_page
)


class KanduanjuSpider:
    """52kanduanju.com 爬虫主类"""
    
    def __init__(self):
        # 创建数据目录
        os.makedirs(DATA_DIR, exist_ok=True)
        
        # 设置日志
        self.logger = setup_logging(LOG_FILE, LOG_LEVEL)
        
        # 初始化数据管理器
        self.data_manager = DataManager(DRAMAS_FILE, ACTORS_FILE, CATEGORIES_FILE)
        
        # 创建HTTP会话
        self.session = requests.Session()
        
        # 已处理的短剧ID集合（避免重复处理）
        self.processed_drama_ids: Set[str] = set()
        
        self.logger.info("52kanduanju.com 爬虫初始化完成")
    
    def crawl_main_page(self) -> List[Drama]:
        """爬取主页短剧列表"""
        self.logger.info("开始爬取主页...")
        
        response = make_request(MAIN_PAGE_URL, self.session)
        if not response:
            self.logger.error("无法获取主页内容")
            return []
        
        dramas = parse_drama_list_from_html(response.text)
        self.logger.info(f"主页解析到 {len(dramas)} 个短剧")
        
        return dramas
    
    def crawl_category_page(self, category: dict) -> List[Drama]:
        """爬取分类页面"""
        category_url = CATEGORY_URL_TEMPLATE.format(category_slug=category['slug'])
        self.logger.info(f"开始爬取分类页面: {category['name']} - {category_url}")
        
        response = make_request(category_url, self.session)
        if not response:
            self.logger.error(f"无法获取分类页面: {category['name']}")
            return []
        
        dramas = parse_drama_list_from_html(response.text)
        self.logger.info(f"分类 {category['name']} 解析到 {len(dramas)} 个短剧")
        
        # 添加分类信息到数据管理器
        category_obj = Category(
            name=category['name'],
            slug=category['slug'],
            count=len(dramas),
            url=category_url
        )
        self.data_manager.add_category(category_obj)
        
        return dramas
    
    def crawl_drama_detail(self, drama: Drama) -> Drama:
        """爬取短剧详情页"""
        if not drama.detail_url:
            return drama
        
        self.logger.info(f"爬取短剧详情: {drama.title}")
        
        response = make_request(drama.detail_url, self.session)
        if not response:
            self.logger.error(f"无法获取短剧详情页: {drama.title}")
            return drama
        
        # 解析详情页内容
        updated_drama = parse_drama_detail_page(response.text, drama)
        return updated_drama
    
    def process_dramas(self, dramas: List[Drama]) -> None:
        """处理短剧列表"""
        for drama in dramas:
            if drama.drama_id in self.processed_drama_ids:
                continue
            
            # 爬取详情页
            detailed_drama = self.crawl_drama_detail(drama)
            
            # 添加到数据管理器
            self.data_manager.add_drama(detailed_drama)
            
            # 处理演员信息
            for actor_name in detailed_drama.actors:
                if actor_name:
                    actor = Actor(name=actor_name)
                    self.data_manager.add_actor(actor)
            
            # 标记为已处理
            self.processed_drama_ids.add(drama.drama_id)
            
            self.logger.info(f"处理完成: {detailed_drama.title}")
    
    def crawl_all(self) -> None:
        """爬取所有数据"""
        self.logger.info("开始全量爬取...")
        
        # 加载已有数据
        self.data_manager.load_from_json()
        self.processed_drama_ids = set(self.data_manager.dramas.keys())
        self.logger.info(f"加载已有数据: {len(self.processed_drama_ids)} 个短剧")
        
        all_dramas = []
        
        # 1. 爬取主页
        main_page_dramas = self.crawl_main_page()
        all_dramas.extend(main_page_dramas)
        
        # 2. 爬取各分类页面
        for category in CATEGORIES:
            if category['slug'] == 'all':  # 跳过"全部"分类，避免重复
                continue
            
            category_dramas = self.crawl_category_page(category)
            all_dramas.extend(category_dramas)
            
            # 每个分类后暂停一下
            time.sleep(2)
        
        # 3. 去重处理
        unique_dramas = {}
        for drama in all_dramas:
            if drama.drama_id not in unique_dramas:
                unique_dramas[drama.drama_id] = drama
        
        self.logger.info(f"去重后共 {len(unique_dramas)} 个短剧")
        
        # 4. 处理短剧详情
        self.process_dramas(list(unique_dramas.values()))
        
        # 5. 保存数据
        self.save_data()
        
        self.logger.info("全量爬取完成")
    
    def save_data(self) -> None:
        """保存数据到文件"""
        self.logger.info("保存数据到文件...")
        
        try:
            self.data_manager.save_to_json()
            stats = self.data_manager.get_stats()
            self.logger.info(f"数据保存完成: {stats}")
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
    
    def get_stats(self) -> dict:
        """获取爬取统计信息"""
        return self.data_manager.get_stats()


def main():
    """主函数"""
    spider = KanduanjuSpider()
    
    try:
        spider.crawl_all()
        stats = spider.get_stats()
        print(f"\n爬取完成！统计信息:")
        print(f"- 短剧数量: {stats['dramas_count']}")
        print(f"- 演员数量: {stats['actors_count']}")
        print(f"- 分类数量: {stats['categories_count']}")
        print(f"\n数据文件:")
        print(f"- 短剧数据: {DRAMAS_FILE}")
        print(f"- 演员数据: {ACTORS_FILE}")
        print(f"- 分类数据: {CATEGORIES_FILE}")
        
    except KeyboardInterrupt:
        print("\n用户中断爬取")
        spider.save_data()
    except Exception as e:
        print(f"\n爬取过程中出现错误: {e}")
        spider.save_data()


if __name__ == "__main__":
    main()
