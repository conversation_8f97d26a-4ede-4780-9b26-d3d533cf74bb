#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员信息提取脚本
从 search_user_results.json 中提取有用信息，针对 duanju_actors_need_scrape.json 中的演员
使用硅基流动的 LLM 服务处理和汇总信息
"""

import json
import os
import re
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# 硅基流动 API 配置
SILICONFLOW_API_KEY = "sk-bntstadiguzdjgxczscotiatbawbqfgwerywdrznhjtffhix"
SILICONFLOW_API_URL = "https://api.siliconflow.cn/v1/chat/completions"
MODEL_NAME = "deepseek-ai/DeepSeek-V3"

def load_target_actors() -> Dict[str, str]:
    """加载目标演员列表"""
    try:
        with open("duanju_actors_need_scrape.json", "r", encoding="utf-8") as f:
            actors = json.load(f)
            return {str(actor["id"]): actor["name"] for actor in actors}
    except Exception as e:
        print(f"读取目标演员列表失败: {e}")
        return {}

def load_search_results() -> Dict[str, Any]:
    """加载搜索结果"""
    try:
        with open("search_user_results.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取搜索结果失败: {e}")
        return {}

def call_llm_api(prompt: str, max_retries: int = 3) -> Optional[str]:
    """调用硅基流动 LLM API"""
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": MODEL_NAME,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的信息提取助手，擅长从网页内容中提取演员的社交媒体账号信息和个人简介。请严格按照要求的JSON格式返回结果。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 1500
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(SILICONFLOW_API_URL, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"API 响应格式异常: {result}")

        except requests.exceptions.Timeout:
            print(f"API 调用超时 (尝试 {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                import time
                time.sleep(5)
        except requests.exceptions.RequestException as e:
            print(f"API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                import time
                time.sleep(2 ** attempt)  # 指数退避

    print("API 调用最终失败，使用备用方案")
    return None

def extract_social_media_info(actor_name: str, search_data: List[Dict]) -> Dict[str, Any]:
    """使用 LLM 提取社交媒体信息"""
    
    # 准备搜索数据摘要
    content_summary = []
    for item in search_data[:5]:  # 只取前5个结果避免内容过长
        title = item.get("title", "")
        description = item.get("description", "")
        url = item.get("url", "")
        content_summary.append(f"标题: {title}\n描述: {description}\nURL: {url}")
    
    content_text = "\n\n---\n\n".join(content_summary)
    
    prompt = f"""
请从以下关于演员"{actor_name}"的搜索结果中提取信息，返回JSON格式：

搜索结果：
{content_text}

请提取以下信息并以JSON格式返回：
{{
    "xiaohongshu_id": "小红书账号ID或用户名（如果找到）",
    "weibo_id": "微博账号ID或用户名（如果找到）", 
    "weibo_super_topic_id": "微博超话ID（如果找到）",
    "douyin_id": "抖音账号ID或用户名（如果找到）",
    "actor_bio": "演员简介（汇总所有来源的信息，去重后的完整介绍）",
    "sources": ["信息来源列表"]
}}

注意：
1. 如果某项信息未找到，请设置为 null
2. 演员简介需要汇总多个来源的信息，去除重复内容
3. 只返回JSON，不要其他文字
4. 确保JSON格式正确
"""

    llm_response = call_llm_api(prompt)
    
    if llm_response:
        try:
            # 尝试解析JSON
            import json
            # 清理响应，移除可能的markdown标记
            clean_response = llm_response.strip()
            if clean_response.startswith("```json"):
                clean_response = clean_response[7:]
            if clean_response.endswith("```"):
                clean_response = clean_response[:-3]
            clean_response = clean_response.strip()
            
            return json.loads(clean_response)
        except json.JSONDecodeError as e:
            print(f"LLM 响应解析失败: {e}")
            print(f"原始响应: {llm_response}")
    
    # 如果 LLM 失败，使用简单的正则表达式提取
    return extract_info_with_regex(actor_name, search_data)

def extract_info_with_regex(actor_name: str, search_data: List[Dict]) -> Dict[str, Any]:
    """使用正则表达式提取基本信息（备用方案）"""
    result = {
        "xiaohongshu_id": None,
        "weibo_id": None,
        "weibo_super_topic_id": None,
        "douyin_id": None,
        "actor_bio": "",
        "sources": []
    }
    
    all_text = ""
    for item in search_data:
        title = item.get("title", "")
        description = item.get("description", "")
        url = item.get("url", "")
        markdown = item.get("markdown", "")
        
        all_text += f"{title} {description} {markdown} "
        
        # 提取URL中的信息
        if "xiaohongshu.com" in url or "xhs.com" in url:
            result["sources"].append(url)
        elif "weibo.com" in url:
            result["sources"].append(url)
        elif "douyin.com" in url:
            result["sources"].append(url)
    
    # 简单的描述提取
    descriptions = []
    for item in search_data:
        desc = item.get("description", "")
        if desc and len(desc) > 20:
            descriptions.append(desc)
    
    if descriptions:
        result["actor_bio"] = " ".join(descriptions[:3])  # 取前3个描述
    
    return result

def process_actors():
    """处理所有演员信息"""
    target_actors = load_target_actors()
    search_results = load_search_results()
    
    if not target_actors:
        print("没有找到目标演员列表")
        return
    
    if not search_results:
        print("没有找到搜索结果")
        return
    
    extracted_info = {}
    processed_count = 0
    
    print(f"开始处理 {len(target_actors)} 个演员的信息...")
    
    for actor_id, actor_name in target_actors.items():
        if actor_id in search_results:
            search_data = search_results[actor_id]
            
            if search_data.get("success") and search_data.get("data"):
                print(f"处理演员: {actor_name} (ID: {actor_id})")
                
                # 提取信息
                info = extract_social_media_info(actor_name, search_data["data"])
                
                extracted_info[actor_id] = {
                    "name": actor_name,
                    "xiaohongshu_id": info.get("xiaohongshu_id"),
                    "weibo_id": info.get("weibo_id"),
                    "weibo_super_topic_id": info.get("weibo_super_topic_id"),
                    "douyin_id": info.get("douyin_id"),
                    "actor_bio": info.get("actor_bio", ""),
                    "sources": info.get("sources", []),
                    "extracted_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                processed_count += 1
                
                # 每处理3个演员暂停一下，避免API限流
                if processed_count % 3 == 0:
                    print(f"已处理 {processed_count} 个演员，暂停5秒...")
                    import time
                    time.sleep(5)

                    # 每处理10个演员保存一次中间结果
                    if processed_count % 10 == 0:
                        temp_file = f"extracted_actor_info_temp_{processed_count}.json"
                        try:
                            with open(temp_file, "w", encoding="utf-8") as f:
                                json.dump(extracted_info, f, ensure_ascii=False, indent=2)
                            print(f"中间结果已保存到: {temp_file}")
                        except Exception as e:
                            print(f"保存中间结果失败: {e}")
            else:
                print(f"演员 {actor_name} (ID: {actor_id}) 的搜索结果无效")
        else:
            print(f"演员 {actor_name} (ID: {actor_id}) 没有搜索结果")
    
    # 保存结果
    output_file = "extracted_actor_info.json"
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(extracted_info, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 信息提取完成！")
        print(f"处理了 {processed_count} 个演员")
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果失败: {e}")
    
    # 创建统计报告
    create_summary_report(extracted_info)

def create_summary_report(extracted_info: Dict[str, Any]):
    """创建统计报告"""
    total_actors = len(extracted_info)
    has_xiaohongshu = sum(1 for info in extracted_info.values() if info.get("xiaohongshu_id"))
    has_weibo = sum(1 for info in extracted_info.values() if info.get("weibo_id"))
    has_weibo_topic = sum(1 for info in extracted_info.values() if info.get("weibo_super_topic_id"))
    has_douyin = sum(1 for info in extracted_info.values() if info.get("douyin_id"))
    has_bio = sum(1 for info in extracted_info.values() if info.get("actor_bio"))
    
    report = {
        "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_actors_processed": total_actors,
        "statistics": {
            "xiaohongshu_accounts_found": has_xiaohongshu,
            "weibo_accounts_found": has_weibo,
            "weibo_super_topics_found": has_weibo_topic,
            "douyin_accounts_found": has_douyin,
            "actor_bios_extracted": has_bio
        },
        "coverage_rates": {
            "xiaohongshu_coverage": f"{(has_xiaohongshu/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_coverage": f"{(has_weibo/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "weibo_topic_coverage": f"{(has_weibo_topic/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "douyin_coverage": f"{(has_douyin/total_actors*100):.1f}%" if total_actors > 0 else "0%",
            "bio_coverage": f"{(has_bio/total_actors*100):.1f}%" if total_actors > 0 else "0%"
        }
    }
    
    # 保存统计报告
    report_file = "extraction_summary_report.json"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"统计报告已保存到: {report_file}")
    except Exception as e:
        print(f"保存统计报告失败: {e}")
    
    # 打印统计信息
    print(f"\n📊 提取统计:")
    print(f"总处理演员数: {total_actors}")
    print(f"小红书账号: {has_xiaohongshu} ({report['coverage_rates']['xiaohongshu_coverage']})")
    print(f"微博账号: {has_weibo} ({report['coverage_rates']['weibo_coverage']})")
    print(f"微博超话: {has_weibo_topic} ({report['coverage_rates']['weibo_topic_coverage']})")
    print(f"抖音账号: {has_douyin} ({report['coverage_rates']['douyin_coverage']})")
    print(f"演员简介: {has_bio} ({report['coverage_rates']['bio_coverage']})")

if __name__ == "__main__":
    process_actors()
