
import logging
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from apis.duanju import duanju
import uvicorn


# 日志初始化
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s %(message)s",
)
logger = logging.getLogger("fastapi-app")

app = FastAPI(
    title="FastAPI 短剧服务",
    description="短剧相关 API 服务，支持多业务扩展",
    version="0.1.0"
)

# CORS 支持（如有需要可调整 origins）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载 api 路由
app.include_router(duanju.router)

# 示例根路由
@app.get("/")
def read_root():
    return {"message": "Hello, FastAPI!"}


# 启动时打印所有路由和日志初始化
@app.on_event("startup")
def show_routes():
    logger.info("服务启动，已注册路由如下：")
    for route in app.routes:
        methods = getattr(route, 'methods', None)
        path = getattr(route, 'path', None)
        if methods and path:
            logger.info(f"{','.join(methods):10s} {path}")
        elif path:
            logger.info(f"{'':10s} {path}")

import os

if __name__ == "__main__":
    host = os.environ.get("APP_HOST", "0.0.0.0")
    port = int(os.environ.get("APP_PORT", 8000))
    reload = os.environ.get("APP_RELOAD", "true").lower() == "true"
    workers = int(os.environ.get("APP_WORKERS", 1))
    print(f"以开发模式直接运行 main.py，监听 {host}:{port}，reload={reload}，workers={workers}")
    # uvicorn.run 的 workers 仅命令行支持，代码内 run 只支持单进程
    if workers > 1:
        print("[警告] 多进程请用命令行: uvicorn main:app --host {host} --port {port} --reload --workers {workers}")
        uvicorn.run("main:app", host=host, port=port, reload=reload)
    else:
        uvicorn.run("main:app", host=host, port=port, reload=reload)
