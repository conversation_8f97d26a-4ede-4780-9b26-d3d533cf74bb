#!/usr/bin/env python3
"""
批量上传处理状态总结
显示当前的处理进度和统计信息
"""

import json
import os
import glob
from datetime import datetime
from batch_upload_processor import BatchUploadProcessor

def load_all_results():
    """加载所有处理结果文件"""
    result_files = glob.glob("batch_upload_results_*.json")
    all_results = []
    
    for file in sorted(result_files):
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                all_results.append({
                    'file': file,
                    'data': data
                })
        except Exception as e:
            print(f"读取结果文件 {file} 失败: {e}")
    
    return all_results

def analyze_results(all_results):
    """分析处理结果"""
    total_actors_processed = 0
    total_cuts_created = 0
    successful_actors = 0
    failed_actors = 0
    processed_actors = set()
    
    for result_file in all_results:
        data = result_file['data']
        for actor_result in data.get('results', []):
            actor_id = actor_result.get('actor_id')
            if actor_id not in processed_actors:
                processed_actors.add(actor_id)
                total_actors_processed += 1
                
                if actor_result.get('success'):
                    successful_actors += 1
                    cuts = actor_result.get('cuts_created', [])
                    total_cuts_created += len(cuts)
                else:
                    failed_actors += 1
    
    return {
        'total_actors_processed': total_actors_processed,
        'successful_actors': successful_actors,
        'failed_actors': failed_actors,
        'total_cuts_created': total_cuts_created,
        'processed_actors': processed_actors
    }

def show_actor_status():
    """显示演员处理状态"""
    processor = BatchUploadProcessor()
    actors = processor.load_actors_data()
    
    if not actors:
        print("无法加载演员数据")
        return
    
    # 加载处理结果
    all_results = load_all_results()
    analysis = analyze_results(all_results)
    processed_actors = analysis['processed_actors']
    
    print(f"演员处理状态 (共 {len(actors)} 个演员):")
    print("-" * 100)
    print(f"{'索引':<4} {'ID':<4} {'姓名':<10} {'kuaidong_id':<20} {'视频':<6} {'状态':<10}")
    print("-" * 100)
    
    actors_with_videos = 0
    actors_processed = 0
    
    for i, actor in enumerate(actors):
        actor_id = actor.get('id')
        name = actor.get('name', 'Unknown')
        kuaidong_id = actor.get('kuaidong_id', 'N/A')
        
        # 检查视频文件
        video_files = processor.find_video_files(kuaidong_id)
        video_status = f"✓({len(video_files)})" if video_files else "✗"
        if video_files:
            actors_with_videos += 1
        
        # 检查处理状态
        if actor_id in processed_actors:
            process_status = "已处理"
            actors_processed += 1
        elif video_files:
            process_status = "待处理"
        else:
            process_status = "无视频"
        
        print(f"{i:<4} {actor_id:<4} {name:<10} {kuaidong_id:<20} {video_status:<6} {process_status:<10}")
    
    print("-" * 100)
    print(f"统计: 有视频 {actors_with_videos}, 已处理 {actors_processed}, 待处理 {actors_with_videos - actors_processed}")

def show_processing_summary():
    """显示处理总结"""
    all_results = load_all_results()
    
    if not all_results:
        print("没有找到处理结果文件")
        return
    
    analysis = analyze_results(all_results)
    
    print("处理结果总结:")
    print("=" * 60)
    print(f"总处理演员数: {analysis['total_actors_processed']}")
    print(f"成功处理: {analysis['successful_actors']}")
    print(f"处理失败: {analysis['failed_actors']}")
    print(f"总创建Cut数: {analysis['total_cuts_created']}")
    print(f"成功率: {analysis['successful_actors']/analysis['total_actors_processed']*100:.1f}%" if analysis['total_actors_processed'] > 0 else "N/A")
    
    print("\n处理历史:")
    print("-" * 60)
    for result_file in all_results:
        data = result_file['data']
        processed_at = data.get('processed_at', 'Unknown')
        total = data.get('total_processed', 0)
        successful = data.get('successful', 0)
        failed = data.get('failed', 0)
        
        print(f"文件: {result_file['file']}")
        print(f"  时间: {processed_at}")
        print(f"  处理: {total} 个演员 (成功: {successful}, 失败: {failed})")
        
        # 显示详细的Cut创建信息
        total_cuts = 0
        for actor_result in data.get('results', []):
            if actor_result.get('success'):
                cuts = actor_result.get('cuts_created', [])
                total_cuts += len(cuts)
        print(f"  创建: {total_cuts} 个Cut")
        print()

def show_next_steps():
    """显示下一步建议"""
    processor = BatchUploadProcessor()
    actors = processor.load_actors_data()
    
    all_results = load_all_results()
    analysis = analyze_results(all_results)
    processed_actors = analysis['processed_actors']
    
    # 找出有视频但未处理的演员
    unprocessed_with_videos = []
    for actor in actors:
        actor_id = actor.get('id')
        kuaidong_id = actor.get('kuaidong_id')
        if actor_id not in processed_actors and processor.find_video_files(kuaidong_id):
            unprocessed_with_videos.append(actor)
    
    print("下一步建议:")
    print("=" * 60)
    
    if not unprocessed_with_videos:
        print("✓ 所有有视频的演员都已处理完成！")
    else:
        print(f"还有 {len(unprocessed_with_videos)} 个演员有视频但未处理:")
        print("\n建议的处理命令:")
        
        if len(unprocessed_with_videos) == 1:
            actor = unprocessed_with_videos[0]
            print(f"# 处理单个演员: {actor.get('name')}")
            print(f"python run_batch_upload.py --actor-id {actor.get('id')}")
            print(f"# 或者")
            print(f"python run_batch_upload.py --kuaidong-id {actor.get('kuaidong_id')}")
        else:
            print("# 处理所有剩余演员")
            print("python run_batch_upload.py")
            print("\n# 或者分批处理")
            print("python run_batch_upload.py --start 0 --end 5")
        
        print("\n未处理的演员列表:")
        for i, actor in enumerate(unprocessed_with_videos[:10]):  # 只显示前10个
            print(f"  {i+1}. {actor.get('name')} (ID: {actor.get('id')}, kuaidong_id: {actor.get('kuaidong_id')})")
        
        if len(unprocessed_with_videos) > 10:
            print(f"  ... 还有 {len(unprocessed_with_videos) - 10} 个")

def main():
    """主函数"""
    print("批量上传处理状态总结")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示处理总结
    show_processing_summary()
    print()
    
    # 显示演员状态
    show_actor_status()
    print()
    
    # 显示下一步建议
    show_next_steps()

if __name__ == "__main__":
    main()
