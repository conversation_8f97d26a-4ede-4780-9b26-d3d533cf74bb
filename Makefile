########### 通用环境变量 begin #############
# 项目信息
PROJECT_NAME = fastapi-app
VERSION = 0.1.0

# git 仓库基本信息
BRANCH_NAME = $(shell git rev-parse --abbrev-ref HEAD)
BUILD_TS = $(shell TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S")
COMMIT_ID = $(shell git rev-parse --short=8 HEAD)
CHANNEL := $(subst /,-,$(or $(CI_COMMIT_REF_NAME), $(BRANCH_NAME)))


# docker 信息
DOCKER_IMAGE_PRE = registry.cn-shanghai.aliyuncs.com/jony4
BASE_IMAGE := registry.cn-shanghai.aliyuncs.com/jony4base/python:3.8.18
IMAGE_FLAGS :=
IMAGE_FLAGS := $(IMAGE_FLAGS) --build-arg PROJECT_NAME=$(PROJECT_NAME)
IMAGE_FLAGS := $(IMAGE_FLAGS) --build-arg BASE_IMAGE=$(BASE_IMAGE)

########### 通用环境变量 end #############

########### 必备命令 begin #############
.DEFAULT: all

.PHONY : all install fmt lint test run clean image baseimage


# all: 构建基础镜像、业务镜像、并完成常规检查
all: baseimage image install fmt lint test

install:
	pip install --upgrade pip
	pip install -r requirements.txt

fmt:
	black .

lint:
	flake8 .

test:
	pytest

run:
	uvicorn main:app --reload

clean:
	find . -type d -name '__pycache__' -exec rm -rf {} +
	find . -type f -name '*.pyc' -delete
	rm -rf .pytest_cache .coverage .mypy_cache
########### 必备命令 end #############

online:
online_config_only:
########## 部署到机器上 ##############
# 如需远程部署，可自定义以下变量和命令
dev_host=root@127.0.0.1

deploy:
	scp -r . $(dev_host):/data/apps/$(PROJECT_NAME)/
	ssh $(dev_host) -p 22 "cd /data/apps/$(PROJECT_NAME) && pip install -r requirements.txt && supervisorctl restart $(PROJECT_NAME)"



# 构建基础镜像
baseimage:
	docker build -f Dockerfile.base -t $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME)-base:$(VERSION) .
	docker tag $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME)-base:$(VERSION) $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME)-base:latest
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME)-base:$(VERSION)
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME)-base:latest

# 构建业务镜像
image:
	docker build $(IMAGE_FLAGS) -t $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID) .
	docker tag $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID) $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):latest
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID)
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):latest

image-nocache:
	docker build --no-cache $(IMAGE_FLAGS) -t $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID) .
	docker tag $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID) $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):latest
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):$(VERSION)-$(CHANNEL)-$(COMMIT_ID)
	docker push $(DOCKER_IMAGE_PRE)/$(PROJECT_NAME):latest