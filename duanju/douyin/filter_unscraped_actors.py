#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筛选还没有爬过的演员脚本
从 duanju_actors_need_scrape.json 中筛选出还没有在 search_user_results.json 中的演员
"""

import json
import os
from datetime import datetime

def load_scraped_results():
    """加载已经爬取的结果"""
    results_file = "search_user_results.json"
    if not os.path.exists(results_file):
        print(f"警告: {results_file} 文件不存在，将视为没有已爬取的数据")
        return set()
    
    try:
        with open(results_file, "r", encoding="utf-8") as f:
            results = json.load(f)
            scraped_ids = set(results.keys())
            print(f"已爬取的演员数量: {len(scraped_ids)}")
            print(f"已爬取的演员ID: {sorted(scraped_ids)}")
            return scraped_ids
    except Exception as e:
        print(f"读取 {results_file} 失败: {e}")
        return set()

def load_actors_to_scrape():
    """加载需要爬取的演员列表"""
    actors_file = "duanju_actors_need_scrape.json"
    if not os.path.exists(actors_file):
        print(f"错误: {actors_file} 文件不存在")
        return []
    
    try:
        with open(actors_file, "r", encoding="utf-8") as f:
            actors = json.load(f)
            print(f"需要爬取的演员总数: {len(actors)}")
            return actors
    except Exception as e:
        print(f"读取 {actors_file} 失败: {e}")
        return []

def filter_unscraped_actors(actors, scraped_ids):
    """筛选出还没有爬过的演员"""
    unscraped_actors = []
    
    for actor in actors:
        actor_id = str(actor.get("id", ""))  # 确保ID是字符串类型
        if actor_id and actor_id not in scraped_ids:
            unscraped_actors.append(actor)
    
    return unscraped_actors

def save_unscraped_actors(unscraped_actors, output_file="duanju_actors.json"):
    """保存还没有爬过的演员到文件"""
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(unscraped_actors, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(unscraped_actors)} 个未爬取的演员到 {output_file}")
        return True
    except Exception as e:
        print(f"保存文件 {output_file} 失败: {e}")
        return False

def create_operation_record(scraped_count, total_count, unscraped_count, operation_dir):
    """创建操作记录"""
    record = {
        "operation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "operation_type": "filter_unscraped_actors",
        "total_actors_to_scrape": total_count,
        "already_scraped_count": scraped_count,
        "unscraped_count": unscraped_count,
        "output_file": "duanju_actors.json",
        "description": "筛选出还没有爬过的演员，准备进行批量爬取"
    }
    
    record_file = os.path.join(operation_dir, "filter_operation_record.json")
    try:
        with open(record_file, "w", encoding="utf-8") as f:
            json.dump(record, f, ensure_ascii=False, indent=2)
        print(f"操作记录已保存到: {record_file}")
    except Exception as e:
        print(f"保存操作记录失败: {e}")

def main():
    print("=" * 50)
    print("开始筛选还没有爬过的演员")
    print("=" * 50)
    
    # 1. 加载已经爬取的结果
    scraped_ids = load_scraped_results()
    
    # 2. 加载需要爬取的演员列表
    actors_to_scrape = load_actors_to_scrape()
    if not actors_to_scrape:
        print("没有找到需要爬取的演员列表，退出")
        return
    
    # 3. 筛选出还没有爬过的演员
    unscraped_actors = filter_unscraped_actors(actors_to_scrape, scraped_ids)
    
    print(f"\n筛选结果:")
    print(f"需要爬取的演员总数: {len(actors_to_scrape)}")
    print(f"已经爬取的演员数量: {len(scraped_ids)}")
    print(f"还没有爬过的演员数量: {len(unscraped_actors)}")
    
    if unscraped_actors:
        print(f"\n前10个未爬取的演员:")
        for i, actor in enumerate(unscraped_actors[:10]):
            print(f"  {i+1}. ID: {actor.get('id')}, 姓名: {actor.get('name')}")
        
        if len(unscraped_actors) > 10:
            print(f"  ... 还有 {len(unscraped_actors) - 10} 个演员")
    
    # 4. 保存结果
    if unscraped_actors:
        if save_unscraped_actors(unscraped_actors):
            print(f"\n✅ 成功生成 duanju_actors.json 文件，包含 {len(unscraped_actors)} 个待爬取演员")
        else:
            print("\n❌ 保存文件失败")
            return
    else:
        print("\n✅ 所有演员都已经爬取完成，无需进一步操作")
        return
    
    # 5. 创建操作记录
    operations_path = "../../operations"
    if os.path.exists(operations_path):
        operation_dirs = [d for d in os.listdir(operations_path) if d.startswith("douyin_actor_scrape_")]
        if operation_dirs:
            latest_operation_dir = os.path.join(operations_path, sorted(operation_dirs)[-1])
            create_operation_record(len(scraped_ids), len(actors_to_scrape), len(unscraped_actors), latest_operation_dir)
    
    print("=" * 50)
    print("筛选完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
