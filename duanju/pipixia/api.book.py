import requests
import os
import json
import glob
import traceback
import random
import time
import concurrent.futures

url = "https://api.fanqiesdk.com/api/novel/book/page/data/v1/?request_tag_from=lynx&sdk_type=3&category_name=playlet&item_id=xx&parent_enterfrom=playlet_category&book_id=7428138598202887230&module_name=novel_channel_recommend&iid=1725565850678041&device_id=4346801548690859&ac=wifi&channel=oppo_1319_tr_64&aid=1319&app_name=super&version_code=556&version_name=5.5.6&device_platform=android&os=android&ssmix=a&device_type=IN2020&device_brand=OnePlus&language=zh&os_api=31&os_version=12&manifest_version_code=556&resolution=1440*2980&dpi=640&update_version_code=55650&_rticket=1749774932756&cdid=86a0f922-79ac-4b00-ac6e-25c9420a4c69&recommend_disable=0&carrier_region=CN&app_language=ZH&app_region=CN&sys_region=CN&update_install_update_version=55650&time_zone=Asia%2FShanghai&last_channel=&last_update_version_code=0&ts=1749774932"

headers = {
  'User-Agent': "com.sup.android.superb/556 (Linux; U; Android 12; zh_CN; IN2020; Build/RKQ1.211119.001; Cronet/TTNetVersion:b714bfef 2024-09-13 QuicVersion:c459d547 2024-08-27)",
  'Accept-Encoding': "gzip, deflate",
  'x-vc-bdturing-sdk-version': "3.7.2.cn",
  'x-tt-trace-id': "00-66b69cb20df71649a3805ab74a7a0527-66b69cb20df71649-01",
  'X-Argus': "VXJLaA==",
  'X-Gorgon': "8404c0d50000d4652249dea9fbd07002f16253cb2b04885bdfb2",
  'X-Helios': "fzAAbOoDszRT/Ivu/QAmajy+I8LKaE9jRUCs8nJb9vlgr+X/",
  'X-Khronos': "1749774933",
  'X-Ladon': "aEtyVQ==",
  'X-Medusa': "VnJLaKKaFJKCpZdTg1iDGAK9KnAuiAAB0qEjAAJYA1UKOGhuvUgEtc1HPnM0ixc20f6GHcG8E1odeBX/1lNyaCpYKH/vax2S23e8x50fehKBfJ/PjhUqCponNCMc5jZPNzGw8ikR48C29BQj7ISRQ+jrKCJUaWdpp23k8Sg1tmpjP3NMjGPez0B/dx+W7CF1dpOg1kfC+feqygjiBvI07GFavpBDHMGoEPrWwpDIcfLHonnaI16mVmiwvNSl+1hEmcruFXrELPajusYSA+jPkzl7FbEa2+aSJrHlv/mWZ1i7FYh9PPk7q1WNeNj+eT0qQEUCYhSB6M4tlvg6ZPfp1WvgpzuijiHFfkATarla0cE43WEgssu+uef1RhLq6lh9JyZWHEqqBWjcWOKxarSKhPL9Lem2QYJJA9b0xMOXxpemlZonfp864ZVamXIigLif6G+Kk491b90c8O9u9aBLCds00nXpxcGJSRLfeuthflin612JL9Uyt4aRJiRUV5Ov8aScOXXuxWUhB9j583AEGXFLTBG90mwLYrcmzi9ACBkZ4odGmH9DNlG9w7byJdWYUPULCm9Ng2YhSDpYCUmLuMv1FLwMFWxbyQp6tXLpWb6Fff6LD7KmHMH5cgcdhzNE5BhTA8tHesZ1tDw6DOd9zOHRyxXauRET7XLBqa98nbVMx86KXlOK/9sXUOMAar9f2ZYQU24K/3cvZ7x/1oTcVuQGCiNaU7ZMByKovjgucUbmn86hFnj6SDhnP80dCwGaZhD/CbL1kcoWBJq0Q6swLo/IR1hJ/vRcf/sXtIasw0XEy4dXAa1G5FQ6bBhsZzvrCQpyxIdEtyDy8OjSIVBe5gELzOtkhOZ75ql8q4ht4UHNt6ePjKmzGOpOdbY5mOGJm90VKBRwGW2OKjCKJ2n8AMKQoMXwvC8pS8Y8fNkm2m3nOsALkFmN6V++///Hvv//xwdG"
}



LIST_DIR = '/Users/<USER>/Documents/短剧/皮皮虾/列表'
DETAIL_DIR = '/Users/<USER>/Documents/短剧/皮皮虾/详情'

os.makedirs(DETAIL_DIR, exist_ok=True)

json_files = glob.glob(os.path.join(LIST_DIR, '*.json'))

# 只做详情请求和保存，不再处理图片下载
for file_path in json_files:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
            except Exception as e:
                print(f'JSON decode error in {file_path}: {e}')
                continue
        # 只处理 data[0].feed_data.book_data
        try:
            book_list = data['data'][0]['feed_data']['book_data']
        except Exception as e:
            print(f'Key error in {file_path}: {e}')
            continue
        for item in book_list:
            try:
                book_id = item['book_data']['book_id']
                out_path = os.path.join(DETAIL_DIR, f'{book_id}.json')
                # 检查文件是否存在且大于1KB
                if os.path.exists(out_path) and os.path.getsize(out_path) > 1024:
                    print(f"{out_path} 已存在且大于1KB，跳过。")
                    continue
                # 替换 url 中的 book_id
                req_url = url.replace('book_id=7428138598202887230', f'book_id={book_id}')
                resp = requests.get(req_url, headers=headers)
                # 保存 response
                with open(out_path, 'w', encoding='utf-8') as out_f:
                    out_f.write(resp.text)
            except Exception as e:
                print(f'Error processing book_id in {file_path}: {e}\n{traceback.format_exc()}')
                continue
    except Exception as e:
        print(f'Error processing file {file_path}: {e}\n{traceback.format_exc()}')
        continue
print("处理完成！所有详情数据已保存。")