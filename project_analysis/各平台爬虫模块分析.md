# 各平台爬虫模块分析

## 1. 皮皮虾平台 (pipixia)

### 1.1 模块概述
- **目标平台**: 皮皮虾短剧平台
- **主要功能**: 短剧数据爬取、演员信息获取、数据库操作
- **技术栈**: Python + HTTP Client + NocoBase API

### 1.2 核心文件分析

#### `nocobase.duanju.create.py` - 短剧数据创建
```python
def create_duanju(abstract, actors, author, book_name, book_id, 
                 category_tags, category, serial_count, read_count, 
                 read_count_last_updated_ts, thumb_url):
    url = "https://data.wansu.tech/api/duanju:create"
    payload = {
        "abstract": abstract,
        "actors": actors,
        "author": author,
        "book_name": book_name,
        "book_id": book_id,
        "category_tags": category_tags,
        "platform": "ppx",
        "category": category,
        "serial_count": serial_count,
        "read_count": read_count,
        "read_count_last_updated_ts": read_count_last_updated_ts,
        "thumb_url": thumb_url
    }
```

**功能特点**:
- 批量数据创建
- 重复数据检查
- 周期性保存机制
- 错误处理和重试

#### `api.book.py` - 书籍API调用
- **功能**: 获取书籍详细信息
- **数据格式**: JSON响应解析
- **缓存机制**: 本地文件缓存

#### `api.loadmore.py` - 分页加载
- **功能**: 处理分页数据获取
- **策略**: 增量加载
- **性能**: 批量处理优化

### 1.3 数据处理流程
```
API调用 → JSON解析 → 数据清洗 → 格式转换 → 数据库存储
    ↓
本地缓存 → 重复检查 → 批量提交 → 结果验证
```

### 1.4 技术亮点
- **智能去重**: 基于book_id的重复检查
- **容错机制**: 异常捕获和继续处理
- **性能优化**: 批量操作和周期性保存

## 2. 抖音平台 (douyin)

### 2.1 模块概述
- **目标平台**: 抖音短视频平台
- **主要功能**: 用户搜索、演员信息获取
- **技术栈**: Python + Firecrawl API

### 2.2 核心实现

#### `search_user.py` - 用户搜索
```python
def search_user(query: str) -> Dict[str, Any]:
    url = "https://api.firecrawl.dev/v1/search"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer fc-859daa64631e4b71bc3b062b727bad06"
    }
    data = {
        "query": query + " 短剧 演员",
        "limit": 5,
        "scrapeOptions": {
            "formats": ["markdown", "links"]
        }
    }
```

**技术特点**:
- **第三方API**: 使用Firecrawl搜索服务
- **关键词优化**: 自动添加"短剧 演员"后缀
- **多格式支持**: Markdown和链接格式
- **API认证**: Bearer Token认证

#### `search_user_batch.py` - 批量搜索
- **功能**: 批量处理演员搜索
- **数据源**: JSON文件读取
- **输出**: 结构化JSON存储

### 2.3 数据文件
- `duanju_actors.json`: 演员基础信息
- `duanju_actors_with_douyin.json`: 包含抖音信息的演员数据
- `search_user_results.json`: 搜索结果缓存

### 2.4 数据库操作
- `update.sql`: 数据库更新脚本
- `update_douyin_id.py`: 抖音ID更新逻辑

## 3. 小红书平台 (xiaohongshu)

### 3.1 模块概述
- **目标平台**: 小红书社交平台
- **主要功能**: 演员搜索
- **技术栈**: Python + HTTP Client

### 3.2 核心实现

#### `search.duanju.actor.py` - 演员搜索
```python
def search_xhs_actor(keyword="王小亿", payload=None, headers=None):
    if payload is None:
        payload = '{"keyword":"%s","search_id":"2f077yo74hvoknuivlabz","biz_type":"web_search_user","request_id":"109378910-1751357708338"}' % keyword
    
    conn = http.client.HTTPSConnection("edith.xiaohongshu.com")
    conn.request("POST", "/api/sns/web/v1/search/onebox", payload.encode('utf-8'), headers)
    res = conn.getresponse()
    data = res.read()
    
    if res.getheader('Content-Encoding') == 'gzip':
        data = gzip.decompress(data)
    return data.decode("utf-8")
```

**技术特点**:
- **直接API调用**: 绕过前端限制
- **压缩支持**: gzip解压缩
- **参数化**: 支持自定义搜索参数
- **命令行支持**: 支持命令行参数传入

### 3.3 请求头配置
```python
headers = {
    'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    'Accept': "application/json, text/plain, */*",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'Content-Type': "application/json",
    'pragma': "no-cache",
    'cache-control': "no-cache"
}
```

## 4. 红果平台 (hongguo)

### 4.1 模块概述
- **目标平台**: 红果阅读App
- **主要功能**: 移动端抓包、协议分析
- **技术栈**: Python + Frida + ADB

### 4.2 核心技术

#### `hongguo.py` - 主控制脚本
```python
def start_app_with_adb(package: str, activity: str):
    client = AdbClient(host="127.0.0.1", port=5037)
    device = client.devices()[0]
    device.shell(f"am start -n {package}/{activity}")
    return device.serial

def run_frida_hook(device_id: str, package_name: str):
    hook_script = """
    Interceptor.attach(Module.findExportByName(null, "SSL_CTX_set_min_proto_version"), {
        onEnter: function (args) {
            args[1] = 0x0301; // 强制TLS 1.0
        }
    });
    """
```

**技术特点**:
- **设备控制**: ADB自动化控制
- **动态注入**: Frida实时Hook
- **协议降级**: SSL/TLS协议降级
- **自动化流程**: 启动→注入→监控

#### `r0capture.py` - 通用抓包工具
- **功能**: Android应用层通杀抓包
- **支持**: Android 7-14全版本
- **特点**: 
  - 无视证书校验
  - 支持所有应用层协议
  - 绕过加固保护

### 4.3 JavaScript Hook脚本
```javascript
// downgrade.js - 协议降级脚本
Interceptor.attach(Module.findExportByName(null, "SSL_CTX_set_min_proto_version"), {
    onEnter: function (args) {
        console.log("[*] SSL_CTX_set_min_proto_version called");
        args[1] = 0x0301; // TLS 1.0
    }
});
```

## 5. 国家广电总局 (nrta.gov.cn)

### 5.1 模块概述
- **目标平台**: 国家广播电视总局官网
- **主要功能**: 政策文档下载、新闻爬取
- **技术栈**: Python + HTTP Client

### 5.2 核心功能

#### `nrta_all_news.py` - 新闻爬取
- **功能**: 获取所有新闻列表
- **数据**: 政策法规、行业动态
- **存储**: JSON格式本地存储

#### `download_pdf.py` - PDF文档下载
- **功能**: 批量下载PDF文档
- **管理**: 文件去重和分类存储
- **监控**: 下载进度和错误处理

### 5.3 数据文件
- `nrta_all_news_records.json`: 新闻记录
- `duanju.json`: 短剧相关政策
- `pdfs/`: PDF文档存储目录

## 6. 平台对比分析

### 6.1 技术复杂度对比
| 平台 | 技术复杂度 | 反爬虫程度 | 数据质量 | 维护成本 |
|------|------------|------------|----------|----------|
| 皮皮虾 | 中等 | 低 | 高 | 中等 |
| 抖音 | 低 | 高 | 中等 | 低 |
| 小红书 | 中等 | 中等 | 高 | 中等 |
| 红果 | 高 | 极高 | 高 | 高 |
| 广电总局 | 低 | 无 | 高 | 低 |

### 6.2 数据获取策略
- **API调用**: 皮皮虾、小红书 (直接调用)
- **第三方服务**: 抖音 (Firecrawl代理)
- **移动端逆向**: 红果 (Frida Hook)
- **网页爬取**: 广电总局 (传统爬虫)

### 6.3 扩展性评估
- **易扩展**: 广电总局、抖音
- **中等扩展**: 皮皮虾、小红书
- **难扩展**: 红果 (依赖特定环境)

## 7. 优化建议

### 7.1 统一化改进
- **数据格式**: 统一JSON Schema
- **错误处理**: 统一异常处理机制
- **日志系统**: 统一日志格式
- **配置管理**: 统一配置文件

### 7.2 性能优化
- **并发处理**: 异步IO优化
- **缓存策略**: 多级缓存机制
- **连接池**: HTTP连接复用
- **批量操作**: 数据批量处理

### 7.3 稳定性提升
- **重试机制**: 自动重试策略
- **熔断器**: 服务降级保护
- **监控告警**: 实时状态监控
- **备份策略**: 数据备份机制
