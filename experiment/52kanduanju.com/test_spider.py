"""
52kanduanju.com 测试爬虫 - 只爬取主页数据进行测试
"""

import os
import sys
import requests
import logging
from typing import List

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import MAIN_PAGE_URL, DATA_DIR, LOG_FILE, LOG_LEVEL
from models import DataManager, Drama, Actor
from utils import setup_logging, make_request, parse_drama_list_from_html, parse_drama_detail_page


class TestSpider:
    """测试爬虫 - 只爬取主页和少量详情页"""
    
    def __init__(self):
        # 创建数据目录
        os.makedirs(DATA_DIR, exist_ok=True)
        
        # 设置日志
        self.logger = setup_logging(LOG_FILE, LOG_LEVEL)
        
        # 初始化数据管理器
        self.data_manager = DataManager(
            f"{DATA_DIR}/test_dramas.json",
            f"{DATA_DIR}/test_actors.json", 
            f"{DATA_DIR}/test_categories.json"
        )
        
        # 创建HTTP会话
        self.session = requests.Session()
        
        self.logger.info("测试爬虫初始化完成")
    
    def test_crawl(self):
        """测试爬取功能"""
        self.logger.info("开始测试爬取...")
        
        # 1. 爬取主页
        response = make_request(MAIN_PAGE_URL, self.session)
        if not response:
            self.logger.error("无法获取主页内容")
            return
        
        dramas = parse_drama_list_from_html(response.text)
        self.logger.info(f"主页解析到 {len(dramas)} 个短剧")
        
        # 2. 只处理前5个短剧的详情页
        for i, drama in enumerate(dramas[:5]):
            self.logger.info(f"处理第 {i+1} 个短剧: {drama.title}")
            
            # 爬取详情页
            if drama.detail_url:
                detail_response = make_request(drama.detail_url, self.session)
                if detail_response:
                    detailed_drama = parse_drama_detail_page(detail_response.text, drama)
                    self.data_manager.add_drama(detailed_drama)
                    
                    # 添加演员信息
                    for actor_name in detailed_drama.actors:
                        if actor_name:
                            actor = Actor(name=actor_name)
                            self.data_manager.add_actor(actor)
                else:
                    # 即使没有详情页，也添加基本信息
                    self.data_manager.add_drama(drama)
            else:
                self.data_manager.add_drama(drama)
        
        # 3. 保存数据
        self.data_manager.save_to_json()
        stats = self.data_manager.get_stats()
        
        self.logger.info(f"测试完成: {stats}")
        print(f"测试爬取完成！")
        print(f"- 短剧数量: {stats['dramas_count']}")
        print(f"- 演员数量: {stats['actors_count']}")
        print(f"- 分类数量: {stats['categories_count']}")


if __name__ == "__main__":
    spider = TestSpider()
    spider.test_crawl()
