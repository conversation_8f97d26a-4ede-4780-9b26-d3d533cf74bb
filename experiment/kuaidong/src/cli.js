#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { createInterface } from 'readline';
import { BaikeSearcher } from './searcher.js';
import logger from './logger.js';

const program = new Command();

// 创建readline接口用于交互式输入
function createReadlineInterface() {
  return createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

// 格式化输出结果
function formatResults(results, keyword) {
  if (!results || results.length === 0) {
    console.log(chalk.yellow(`❌ 没有找到关键词 "${keyword}" 的搜索结果`));
    return;
  }

  console.log(chalk.green(`\n🎉 找到 ${results.length} 个搜索结果:`));
  console.log(chalk.gray('=' * 50));
  
  results.forEach((result, index) => {
    console.log(chalk.blue(`\n📋 结果 ${index + 1}:`));
    console.log(chalk.white(`DocID: ${result.DocID}`));
    console.log(chalk.white(`ID: ${result.ID}`));
    console.log(chalk.white(`Subtitle: ${result.Subtitle}`));
    console.log(chalk.white(`Title: ${result.Title}`));
    
    if (index === 0) {
      console.log(chalk.green('\n✨ JSON格式 (第一个结果):'));
      console.log(chalk.cyan(JSON.stringify(result, null, 2)));
    }
  });
}

// 交互式搜索模式
async function interactiveMode() {
  const rl = createReadlineInterface();
  const searcher = new BaikeSearcher({
    headless: true, // 命令行模式使用无头浏览器
    timeout: 30000
  });

  console.log(chalk.blue('🔍 快懂百科搜索工具 - 交互模式'));
  console.log(chalk.gray('输入关键词进行搜索，输入 "exit" 或 "quit" 退出\n'));

  try {
    await searcher.init();
    
    const askQuestion = () => {
      rl.question(chalk.yellow('请输入搜索关键词: '), async (keyword) => {
        keyword = keyword.trim();
        
        if (keyword === 'exit' || keyword === 'quit' || keyword === '') {
          console.log(chalk.green('👋 再见!'));
          rl.close();
          await searcher.close();
          process.exit(0);
        }

        try {
          console.log(chalk.blue(`\n🔍 正在搜索: ${keyword}...`));
          const results = await searcher.search(keyword);
          formatResults(results, keyword);
        } catch (error) {
          console.error(chalk.red(`❌ 搜索失败: ${error.message}`));
          logger.logError('cli_search', error, { keyword });
        }
        
        console.log('\n');
        askQuestion(); // 继续下一次搜索
      });
    };

    askQuestion();
    
  } catch (error) {
    console.error(chalk.red(`❌ 初始化失败: ${error.message}`));
    rl.close();
    await searcher.close();
    process.exit(1);
  }
}

// 单次搜索模式
async function singleSearch(keyword) {
  const searcher = new BaikeSearcher({
    headless: true,
    timeout: 30000
  });

  try {
    console.log(chalk.blue(`🔍 搜索关键词: ${keyword}`));
    
    const results = await searcher.search(keyword);
    formatResults(results, keyword);
    
  } catch (error) {
    console.error(chalk.red(`❌ 搜索失败: ${error.message}`));
    logger.logError('cli_single_search', error, { keyword });
    process.exit(1);
  } finally {
    await searcher.close();
  }
}

// 批量搜索模式
async function batchSearch(keywords) {
  const searcher = new BaikeSearcher({
    headless: true,
    timeout: 30000
  });

  try {
    console.log(chalk.blue(`🔍 批量搜索 ${keywords.length} 个关键词...`));
    
    const results = await searcher.batchSearch(keywords);
    
    console.log(chalk.green('\n🎉 批量搜索完成:'));
    console.log(chalk.gray('=' * 60));
    
    for (const [keyword, result] of Object.entries(results)) {
      console.log(chalk.blue(`\n📋 关键词: ${keyword}`));
      if (result) {
        console.log(chalk.green(`✅ 找到 ${result.length} 个结果`));
        if (result.length > 0) {
          console.log(chalk.cyan(JSON.stringify(result[0], null, 2)));
        }
      } else {
        console.log(chalk.red('❌ 搜索失败'));
      }
    }
    
  } catch (error) {
    console.error(chalk.red(`❌ 批量搜索失败: ${error.message}`));
    logger.logError('cli_batch_search', error, { keywords });
    process.exit(1);
  } finally {
    await searcher.close();
  }
}

// 设置命令行程序
program
  .name('kuaidong-search')
  .description('快懂百科搜索工具')
  .version('1.0.0');

program
  .command('search [keyword]')
  .description('搜索关键词')
  .option('-i, --interactive', '交互模式')
  .option('-b, --batch <keywords...>', '批量搜索多个关键词')
  .action(async (keyword, options) => {
    if (options.interactive) {
      await interactiveMode();
    } else if (options.batch) {
      await batchSearch(options.batch);
    } else if (keyword) {
      await singleSearch(keyword);
    } else {
      await interactiveMode();
    }
  });

// 默认命令
program
  .argument('[keyword]', '搜索关键词')
  .action(async (keyword) => {
    if (keyword) {
      await singleSearch(keyword);
    } else {
      await interactiveMode();
    }
  });

// 解析命令行参数
program.parse();
