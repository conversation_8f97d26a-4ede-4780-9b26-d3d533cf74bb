#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快懂百科 a_bogus 参数逆向分析脚本
分析 bdms.js 文件，提取 a_bogus 生成逻辑
"""

import re
import json
import hashlib
import time
import random
import string
from urllib.parse import urlencode, quote
import base64

class BdmsAnalyzer:
    def __init__(self, bdms_file_path):
        self.bdms_file_path = bdms_file_path
        self.bdms_content = ""
        self.load_bdms_file()
        
    def load_bdms_file(self):
        """加载 bdms.js 文件内容"""
        try:
            with open(self.bdms_file_path, 'r', encoding='utf-8') as f:
                self.bdms_content = f.read()
            print(f"✅ 成功加载 bdms.js 文件，大小: {len(self.bdms_content)} 字符")
        except Exception as e:
            print(f"❌ 加载 bdms.js 文件失败: {e}")
            
    def extract_module_structure(self):
        """提取模块结构"""
        print("\n🔍 分析模块结构...")
        
        # 查找模块定义模式
        module_pattern = r'(\d+):\s*function\s*\([^)]*\)\s*\{'
        modules = re.findall(module_pattern, self.bdms_content)
        
        print(f"📦 发现 {len(modules)} 个模块")
        
        # 查找可能的导出函数
        export_patterns = [
            r'window\.bdms\s*=\s*([^;]+)',
            r'exports?\s*=\s*([^;]+)',
            r'module\.exports\s*=\s*([^;]+)'
        ]
        
        exports = []
        for pattern in export_patterns:
            matches = re.findall(pattern, self.bdms_content)
            exports.extend(matches)
            
        print(f"📤 发现 {len(exports)} 个可能的导出")
        
        return {
            'modules': modules,
            'exports': exports
        }
    
    def search_crypto_functions(self):
        """搜索可能的加密函数"""
        print("\n🔐 搜索加密相关函数...")
        
        # 常见的加密算法特征
        crypto_patterns = {
            'md5': [r'0x67452301', r'0xEFCDAB89', r'0x98BADCFE', r'0x10325476'],
            'sha1': [r'0x67452301', r'0xEFCDAB89', r'0x98BADCFE', r'0x10325476', r'0xC3D2E1F0'],
            'base64': [r'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'],
            'random': [r'Math\.random', r'random'],
            'timestamp': [r'Date\.now', r'getTime', r'timestamp'],
            'encode': [r'encodeURIComponent', r'btoa', r'atob']
        }
        
        found_patterns = {}
        for algo, patterns in crypto_patterns.items():
            found_patterns[algo] = []
            for pattern in patterns:
                matches = re.finditer(pattern, self.bdms_content, re.IGNORECASE)
                for match in matches:
                    start = max(0, match.start() - 100)
                    end = min(len(self.bdms_content), match.end() + 100)
                    context = self.bdms_content[start:end]
                    found_patterns[algo].append({
                        'position': match.start(),
                        'context': context.replace('\n', ' ').strip()
                    })
        
        # 输出发现的模式
        for algo, matches in found_patterns.items():
            if matches:
                print(f"🎯 发现 {algo.upper()} 相关代码 {len(matches)} 处")
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    print(f"   [{i+1}] 位置 {match['position']}: {match['context'][:100]}...")
        
        return found_patterns
    
    def analyze_string_patterns(self):
        """分析字符串模式"""
        print("\n📝 分析字符串模式...")
        
        # 查找长字符串（可能是密钥或常量）
        long_strings = re.findall(r'["\']([a-zA-Z0-9+/=]{20,})["\']', self.bdms_content)
        
        # 查找可能的参数名
        param_patterns = re.findall(r'["\']([a-z_]+)["\']', self.bdms_content)
        param_counts = {}
        for param in param_patterns:
            if len(param) > 2:  # 过滤太短的
                param_counts[param] = param_counts.get(param, 0) + 1
        
        # 按出现频率排序
        frequent_params = sorted(param_counts.items(), key=lambda x: x[1], reverse=True)[:20]
        
        print(f"🔤 发现 {len(long_strings)} 个长字符串")
        for i, s in enumerate(long_strings[:5]):
            print(f"   [{i+1}] {s[:50]}...")
            
        print(f"📊 高频参数名 (前20个):")
        for param, count in frequent_params:
            print(f"   {param}: {count} 次")
            
        return {
            'long_strings': long_strings,
            'frequent_params': frequent_params
        }
    
    def generate_abogus_hypothesis(self, url, data=None):
        """基于分析生成 a_bogus 参数的假设实现"""
        print("\n🧪 生成 a_bogus 参数假设实现...")
        
        # 常见的参数生成方式
        timestamp = str(int(time.time() * 1000))
        random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        
        # 方法1: 基于URL和时间戳的MD5
        method1_input = f"{url}{timestamp}{random_str}"
        method1_hash = hashlib.md5(method1_input.encode()).hexdigest()
        method1_result = base64.b64encode(method1_hash.encode()).decode()[:32]
        
        # 方法2: 基于请求数据的SHA1
        if data:
            data_str = json.dumps(data, separators=(',', ':')) if isinstance(data, dict) else str(data)
        else:
            data_str = ""
        method2_input = f"{url}{data_str}{timestamp}"
        method2_hash = hashlib.sha1(method2_input.encode()).hexdigest()
        method2_result = base64.b64encode(method2_hash.encode()).decode()[:32]
        
        # 方法3: 自定义编码
        method3_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        method3_result = ''.join(random.choices(method3_chars, k=32))
        
        results = {
            'method1_md5_base64': method1_result,
            'method2_sha1_base64': method2_result,
            'method3_random': method3_result,
            'timestamp': timestamp,
            'random_str': random_str
        }
        
        print("🎲 生成的假设结果:")
        for method, result in results.items():
            print(f"   {method}: {result}")
            
        return results
    
    def extract_key_functions(self):
        """提取关键函数"""
        print("\n🔧 提取关键函数...")
        
        # 查找可能的签名生成函数
        function_patterns = [
            r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*\{[^}]*(?:sign|hash|encode|crypt)[^}]*\}',
            r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\([^)]*\)\s*\{[^}]*(?:sign|hash|encode|crypt)[^}]*\}',
        ]
        
        key_functions = []
        for pattern in function_patterns:
            matches = re.finditer(pattern, self.bdms_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                func_name = match.group(1)
                func_body = match.group(0)
                key_functions.append({
                    'name': func_name,
                    'body': func_body[:200] + '...' if len(func_body) > 200 else func_body,
                    'position': match.start()
                })
        
        print(f"⚙️ 发现 {len(key_functions)} 个可能的关键函数")
        for func in key_functions[:5]:
            print(f"   函数: {func['name']} (位置: {func['position']})")
            print(f"   代码片段: {func['body']}")
            print()
            
        return key_functions

def main():
    """主函数"""
    print("🚀 快懂百科 a_bogus 参数逆向分析")
    print("=" * 50)
    
    # 初始化分析器
    bdms_path = "duanju/douyin/kuaidong/bdms.js"
    analyzer = BdmsAnalyzer(bdms_path)
    
    # 执行分析
    module_info = analyzer.extract_module_structure()
    crypto_info = analyzer.search_crypto_functions()
    string_info = analyzer.analyze_string_patterns()
    function_info = analyzer.extract_key_functions()
    
    # 生成假设实现
    test_url = "https://www.baike.com/api/v2/search/getDocSugDataV2"
    test_data = {"args":[{"Query":"meng","Offset":0,"Count":5}]}
    hypothesis = analyzer.generate_abogus_hypothesis(test_url, test_data)
    
    # 保存分析结果
    analysis_result = {
        'timestamp': time.time(),
        'module_info': module_info,
        'crypto_info': {k: len(v) for k, v in crypto_info.items()},  # 只保存数量
        'string_info': {
            'long_strings_count': len(string_info['long_strings']),
            'frequent_params': string_info['frequent_params'][:10]
        },
        'function_info': [{'name': f['name'], 'position': f['position']} for f in function_info],
        'hypothesis': hypothesis
    }
    
    with open('operations/kuaidong_abogus_reverse_20250719_234209/analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print("\n✅ 分析完成，结果已保存到 analysis_result.json")
    print("\n📋 下一步建议:")
    print("1. 使用浏览器开发者工具动态调试 bdms.js")
    print("2. 在关键函数处设置断点")
    print("3. 观察 a_bogus 参数的实际生成过程")
    print("4. 对比不同请求的 a_bogus 值找出规律")

if __name__ == "__main__":
    main()
