// script.js

console.log("[*] Starting OpenSSL downgrade script...");


// 使用 Frida 的 Module API 查找 libssl.so 模块
const targetModule = Process.findModuleByName("libssl.so"); // 替换为目标模块名
if (targetModule) {
    console.log("[*] Found libssl.so at:", targetModule.base);
    // const exports = targetModule.enumerateExports();
    // exports.forEach(function (exp) {
    //     console.log("Exported function:", exp.name);
    // });

    // Process.enumerateModules().forEach(function (module) {
    //     console.log("[*] Module:", module.name, "Base:", module.base, "Size:", module.size);
    // });
} else {
    console.error("[!] libssl.so not found!");
}

// const cm = new CModule(`
// #include <stdio.h>

// void hello(void) {
//   printf("Hello World from CModule\\n");
// }
// `);

// console.log(JSON.stringify(cm));

// const hello = new NativeFunction(cm.hello, 'void', []);
// hello();


Interceptor.attach(Module.getExportByName('libc.so', 'read'), {
  onEnter(args) {
    this.fileDescriptor = args[0].toInt32();
  },
  onLeave(retval) {
    if (retval.toInt32() > 0) {
      /* do something with this.fileDescriptor */
    }
  }
});

// // 使用 Frida 的 Interceptor API Hook OpenSSL 函数
// Interceptor.attach(Module.findExportByName("libssl.so", "SSL_CTX_set_min_proto_version"), {
//     onEnter: function (args) {
//         console.log("[*] Intercepted SSL_CTX_set_min_proto_version");
//         // 修改参数以降级协议版本
//         args[1] = ptr(0x0002); // 例如，将版本设置为 TLS 1.0
//         console.log("[*] Downgraded protocol version to TLS 1.0");
//     },
//     onLeave: function (retval) {
//         console.log("[*] SSL_CTX_set_min_proto_version returned:", retval);
//     }
// });

console.log("[*] OpenSSL downgrade script completed.");