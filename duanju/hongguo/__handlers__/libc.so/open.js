/*
 * Auto-generated by <PERSON><PERSON>. Please modify to match the signature of open.
 * This stub is currently auto-generated from manpages when available.
 *
 * For full API reference, see: https://frida.re/docs/javascript-api/
 */

defineHandler({
  onEnter(log, args, state) {
    log(`open(path="${args[0].readUtf8String()}", oflag=${args[1]}, ...)`);
  },

  onLeave(log, retval, state) {
  }
});
