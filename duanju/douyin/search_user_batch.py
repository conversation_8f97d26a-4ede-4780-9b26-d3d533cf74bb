import json
import time
import os
from typing import Dict, Any
from search_user import search_user

ACTORS_JSON = "duanju_actors.json"
RESULT_JSON = "search_user_results.json"
RATE_LIMIT = 5  # 每分钟最大请求数
SLEEP_INTERVAL = 60 / RATE_LIMIT


def load_actors() -> list:
    with open(ACTORS_JSON, "r", encoding="utf-8") as f:
        return json.load(f)

def load_results() -> Dict[str, Any]:
    if not os.path.exists(RESULT_JSON):
        return {}
    with open(RESULT_JSON, "r", encoding="utf-8") as f:
        try:
            return json.load(f)
        except Exception:
            return {}

def save_results(results: Dict[str, Any]):
    with open(RESULT_JSON, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

def main():
    actors = load_actors()
    results = load_results()
    count = 0
    batch_start = time.time()
    for actor in actors:
        actor_id = actor.get("id")
        name = actor.get("name")
        if not actor_id or not name:
            continue
        if actor_id in results:
            print(f"跳过已完成: {name} ({actor_id})")
            continue
        try:
            print(f"搜索: {name} ({actor_id}) ...")
            resp = search_user(name)
            results[actor_id] = resp
            save_results(results)
            print(f"已保存: {name} ({actor_id})")
        except Exception as e:
            print(f"搜索失败: {name} ({actor_id}) - {e}")
        count += 1
        if count % RATE_LIMIT == 0:
            batch_end = time.time()
            elapsed = batch_end - batch_start
            if elapsed < 60:
                sleep_time = 60 - elapsed
                print(f"限流，已用时 {elapsed:.2f} 秒，休眠 {sleep_time:.2f} 秒...")
                time.sleep(sleep_time)
            else:
                print(f"限流，已用时 {elapsed:.2f} 秒，无需休眠。")
            batch_start = time.time()
        else:
            time.sleep(SLEEP_INTERVAL)

if __name__ == "__main__":
    main()
