#!/usr/bin/env python3
"""
环境测试脚本
检查运行批量上传处理器所需的环境和依赖
"""

import os
import sys
import subprocess
import json
import glob
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("1. 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"   ✓ Python {version.major}.{version.minor}.{version.micro} (满足要求)")
        return True
    else:
        print(f"   ✗ Python {version.major}.{version.minor}.{version.micro} (需要3.7+)")
        return False

def test_ffmpeg():
    """测试FFmpeg是否可用"""
    print("2. 检查FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✓ {version_line}")
            return True
        else:
            print("   ✗ FFmpeg执行失败")
            return False
    except FileNotFoundError:
        print("   ✗ FFmpeg未安装或不在PATH中")
        print("     安装方法:")
        print("     - macOS: brew install ffmpeg")
        print("     - Ubuntu: sudo apt install ffmpeg")
        print("     - Windows: 下载FFmpeg并添加到PATH")
        return False
    except subprocess.TimeoutExpired:
        print("   ✗ FFmpeg执行超时")
        return False

def test_required_packages():
    """测试必需的Python包"""
    print("3. 检查Python依赖包...")
    required_packages = ['requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"   安装命令: pip install {' '.join(missing_packages)}")
        return False
    return True

def test_data_files():
    """测试数据文件是否存在"""
    print("4. 检查数据文件...")
    
    # 检查演员数据文件
    actors_file = "duanju_actors_with_kuaidong.json"
    if os.path.exists(actors_file):
        try:
            with open(actors_file, 'r', encoding='utf-8') as f:
                actors = json.load(f)
            print(f"   ✓ {actors_file} (包含 {len(actors)} 个演员)")
        except Exception as e:
            print(f"   ✗ {actors_file} (读取失败: {e})")
            return False
    else:
        print(f"   ✗ {actors_file} (文件不存在)")
        return False
    
    # 检查数据目录
    data_dir = "kuaidong_data"
    if os.path.exists(data_dir):
        subdirs = [d for d in os.listdir(data_dir) 
                  if os.path.isdir(os.path.join(data_dir, d))]
        print(f"   ✓ {data_dir} (包含 {len(subdirs)} 个子目录)")
        
        # 检查视频文件
        video_count = 0
        for subdir in subdirs[:5]:  # 只检查前5个
            video_dir = os.path.join(data_dir, subdir, "videos")
            if os.path.exists(video_dir):
                videos = glob.glob(os.path.join(video_dir, "*.mp4"))
                video_count += len(videos)
        
        if video_count > 0:
            print(f"   ✓ 找到 {video_count} 个视频文件 (仅检查前5个目录)")
        else:
            print("   ⚠ 未找到视频文件 (可能需要检查目录结构)")
    else:
        print(f"   ✗ {data_dir} (目录不存在)")
        return False
    
    return True

def test_write_permissions():
    """测试写入权限"""
    print("5. 检查文件写入权限...")
    
    # 测试临时目录
    temp_dir = "temp"
    try:
        os.makedirs(temp_dir, exist_ok=True)
        test_file = os.path.join(temp_dir, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"   ✓ {temp_dir} 目录写入权限正常")
    except Exception as e:
        print(f"   ✗ {temp_dir} 目录写入失败: {e}")
        return False
    
    # 测试当前目录
    try:
        test_file = "test_write.txt"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("   ✓ 当前目录写入权限正常")
    except Exception as e:
        print(f"   ✗ 当前目录写入失败: {e}")
        return False
    
    return True

def test_network_connectivity():
    """测试网络连接"""
    print("6. 检查网络连接...")
    try:
        import requests
        response = requests.get("https://data.wansu.tech", timeout=10)
        print(f"   ✓ 可以访问 data.wansu.tech (状态码: {response.status_code})")
        return True
    except Exception as e:
        print(f"   ✗ 网络连接失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("批量上传处理器环境测试")
    print("=" * 60)
    
    tests = [
        test_python_version,
        test_ffmpeg,
        test_required_packages,
        test_data_files,
        test_write_permissions,
        test_network_connectivity
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ✗ 测试异常: {e}")
            results.append(False)
        print()
    
    # 汇总结果
    print("=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有测试通过 ({passed}/{total})")
        print("环境配置正确，可以运行批量上传处理器")
    else:
        print(f"✗ {total - passed} 个测试失败 ({passed}/{total})")
        print("请根据上述提示修复环境问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
