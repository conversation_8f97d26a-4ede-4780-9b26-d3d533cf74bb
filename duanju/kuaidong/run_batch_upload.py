#!/usr/bin/env python3
"""
批量上传运行脚本
提供不同的运行模式和选项
"""

import argparse
import sys
import os
from batch_upload_processor import BatchUploadProcessor

def main():
    parser = argparse.ArgumentParser(description='批量处理短剧演员视频上传')
    
    parser.add_argument('--start', type=int, default=0,
                       help='开始处理的演员索引 (默认: 0)')
    parser.add_argument('--end', type=int, default=None,
                       help='结束处理的演员索引 (默认: 处理到最后)')
    parser.add_argument('--test', action='store_true',
                       help='测试模式，只处理前3个演员')
    parser.add_argument('--actor-id', type=str,
                       help='只处理指定ID的演员')
    parser.add_argument('--kuaidong-id', type=str,
                       help='只处理指定kuaidong_id的演员')
    parser.add_argument('--all-videos', action='store_true',
                       help='处理每个演员的所有视频文件（默认只处理第一个）')
    parser.add_argument('--include-no-videos', action='store_true',
                       help='包括没有视频文件的演员（默认只处理有视频的演员）')
    parser.add_argument('--dry-run', action='store_true',
                       help='干运行模式，不实际上传文件')
    parser.add_argument('--list-actors', action='store_true',
                       help='列出所有演员信息')
    
    args = parser.parse_args()
    
    processor = BatchUploadProcessor()
    
    if args.list_actors:
        list_actors(processor)
        return
    
    if args.test:
        print("测试模式：只处理前3个有视频的演员")
        processor.process_all_actors(start_index=0, end_index=3,
                                   process_all_videos=args.all_videos,
                                   only_with_videos=not args.include_no_videos)
    elif args.actor_id:
        process_single_actor_by_id(processor, args.actor_id, args.all_videos)
    elif args.kuaidong_id:
        process_single_actor_by_kuaidong_id(processor, args.kuaidong_id, args.all_videos)
    else:
        end_index = args.end
        if end_index is not None and end_index <= args.start:
            print("错误：结束索引必须大于开始索引")
            sys.exit(1)

        mode_desc = "所有视频" if args.all_videos else "第一个视频"
        target_desc = "所有演员" if args.include_no_videos else "有视频的演员"
        print(f"批量处理模式：处理{target_desc}的{mode_desc}，从索引 {args.start} 到 {end_index if end_index else '最后'}")
        processor.process_all_actors(start_index=args.start, end_index=end_index,
                                   process_all_videos=args.all_videos,
                                   only_with_videos=not args.include_no_videos)

def list_actors(processor):
    """列出所有演员信息"""
    actors = processor.load_actors_data()
    if not actors:
        print("没有找到演员数据")
        return
    
    print(f"共找到 {len(actors)} 个演员：")
    print("-" * 80)
    print(f"{'索引':<4} {'ID':<4} {'姓名':<10} {'kuaidong_id':<20} {'视频文件'}")
    print("-" * 80)
    
    for i, actor in enumerate(actors):
        actor_id = actor.get('id', 'N/A')
        name = actor.get('name', 'Unknown')
        kuaidong_id = actor.get('kuaidong_id', 'N/A')
        
        # 检查视频文件是否存在
        video_files = processor.find_video_files(kuaidong_id)
        video_status = f"✓ ({len(video_files)})" if video_files else "✗"
        
        print(f"{i:<4} {actor_id:<4} {name:<10} {kuaidong_id:<20} {video_status}")

def process_single_actor_by_id(processor, actor_id, process_all_videos=False):
    """处理指定ID的演员"""
    actors = processor.load_actors_data()
    target_actor = None

    for actor in actors:
        if actor.get('id') == actor_id:
            target_actor = actor
            break

    if not target_actor:
        print(f"未找到ID为 {actor_id} 的演员")
        return

    mode_desc = "所有视频" if process_all_videos else "第一个视频"
    print(f"处理演员: {target_actor.get('name')} (ID: {actor_id}) - {mode_desc}")
    result = processor.process_actor(target_actor, process_all_videos=process_all_videos)
    processor.results.append(result)
    processor.save_results()

def process_single_actor_by_kuaidong_id(processor, kuaidong_id, process_all_videos=False):
    """处理指定kuaidong_id的演员"""
    actors = processor.load_actors_data()
    target_actor = None

    for actor in actors:
        if actor.get('kuaidong_id') == kuaidong_id:
            target_actor = actor
            break

    if not target_actor:
        print(f"未找到kuaidong_id为 {kuaidong_id} 的演员")
        return

    mode_desc = "所有视频" if process_all_videos else "第一个视频"
    print(f"处理演员: {target_actor.get('name')} (kuaidong_id: {kuaidong_id}) - {mode_desc}")
    result = processor.process_actor(target_actor, process_all_videos=process_all_videos)
    processor.results.append(result)
    processor.save_results()

if __name__ == "__main__":
    main()
