import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Logger {
  constructor() {
    this.logsDir = path.join(__dirname, '..', 'logs');
    this.ensureLogsDir();
  }

  ensureLogsDir() {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  getTimestamp() {
    return new Date().toISOString().replace(/[:.]/g, '-');
  }

  getLogFileName(operation) {
    const timestamp = this.getTimestamp();
    return `${operation}_${timestamp}.log`;
  }

  log(operation, data) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      operation,
      data
    };

    const logFileName = this.getLogFileName(operation);
    const logFilePath = path.join(this.logsDir, logFileName);
    
    const logContent = JSON.stringify(logEntry, null, 2) + '\n';
    
    try {
      fs.writeFileSync(logFilePath, logContent);
      console.log(`📝 操作日志已保存: ${logFilePath}`);
    } catch (error) {
      console.error('❌ 保存日志失败:', error.message);
    }
  }

  logSearch(keyword, results, apiUrl) {
    this.log('search', {
      keyword,
      resultsCount: results ? results.length : 0,
      results,
      apiUrl,
      success: !!results
    });
  }

  logError(operation, error, context = {}) {
    this.log('error', {
      operation,
      error: {
        message: error.message,
        stack: error.stack
      },
      context
    });
  }
}

export default new Logger();
