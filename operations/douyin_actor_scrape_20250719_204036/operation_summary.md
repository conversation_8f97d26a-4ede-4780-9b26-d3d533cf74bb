# 抖音演员爬取操作记录

## 操作时间
- 开始时间: 2025-07-19 20:40:36
- 操作人员: AI Assistant

## 操作背景
用户要求完成以下任务：
1. 查看 `duanju/douyin/duanju_actors_need_scrape.json` 文件内容（需要爬取的演员列表）
2. 检查 `search_user_results.json` 中已经爬过的演员
3. 挑选出没爬过的演员
4. 将没有爬过的演员导入到单独文件，然后运行 batch 脚本进行爬取
5. 将所有操作记录保存到单独的文件夹中统一管理

## 操作步骤

### 1. 创建操作记录文件夹
```bash
mkdir -p operations/douyin_actor_scrape_20250719_204036
```

### 2. 分析现有数据
- **已爬取演员数量**: 98个
- **需要爬取演员总数**: 65个
- **实际需要爬取**: 53个（去除已爬取的演员）

### 3. 创建筛选脚本
创建了 `filter_unscraped_actors.py` 脚本，功能包括：
- 读取已爬取的演员ID列表
- 读取需要爬取的演员列表
- 筛选出还没有爬过的演员
- 生成 `duanju_actors.json` 文件供批量脚本使用
- 创建操作记录

### 4. 运行筛选脚本
```bash
cd duanju/douyin
python filter_unscraped_actors.py
```

**筛选结果**:
- 需要爬取的演员总数: 65
- 已经爬取的演员数量: 98
- 还没有爬过的演员数量: 53

**前10个未爬取的演员**:
1. ID: 2383, 姓名: 余茵
2. ID: 23, 姓名: 于龙
3. ID: 664, 姓名: 钟熙
4. ID: 718, 姓名: 贾翼瑄
5. ID: 1349, 姓名: 常丹丹
6. ID: 904, 姓名: 孙樾
7. ID: 455, 姓名: 马小宇
8. ID: 721, 姓名: 陈云廷
9. ID: 666, 姓名: 张集骏
10. ID: 703, 姓名: 姚冠宇

### 5. 启动批量爬取
```bash
python search_user_batch.py
```

批量爬取脚本配置：
- 限流设置: 每分钟最大5个请求
- 自动保存结果到 `search_user_results.json`
- 支持断点续传（跳过已完成的演员）

### 6. 创建监控脚本
创建了 `monitor_scraping_progress.py` 脚本，功能包括：
- 实时监控爬取进度
- 显示完成百分比和剩余数量
- 生成最终报告

### 7. 启动进度监控
```bash
python monitor_scraping_progress.py
```

## 文件结构

### 生成的文件
```
duanju/douyin/
├── filter_unscraped_actors.py          # 筛选脚本
├── monitor_scraping_progress.py        # 监控脚本
├── extract_actor_info_simple.py        # 简化版信息提取脚本
├── extract_actor_info_enhanced.py      # LLM增强版信息提取脚本
├── duanju_actors.json                  # 待爬取演员列表（53个）
├── search_user_results.json            # 爬取结果（持续更新）
├── extracted_actor_info_simple.json    # 简化版提取结果
├── extracted_actor_info_enhanced.json  # 最终增强版提取结果
└── extraction_final_report.json        # 信息提取统计报告

operations/douyin_actor_scrape_20250719_204036/
├── filter_operation_record.json        # 筛选操作记录
├── operation_summary.md                # 本文档
├── scraping_final_report.json          # 爬取最终报告
├── extracted_actor_info_simple.json    # 简化版提取结果（备份）
├── extracted_actor_info_enhanced.json  # 最终提取结果（备份）
└── extraction_final_report.json        # 信息提取报告（备份）
```

### 关键文件说明

#### `duanju_actors.json`
包含53个待爬取演员的完整信息，格式与原始文件一致，供 `search_user_batch.py` 使用。

#### `filter_operation_record.json`
```json
{
  "operation_time": "2025-07-19 20:41:47",
  "operation_type": "filter_unscraped_actors",
  "total_actors_to_scrape": 65,
  "already_scraped_count": 98,
  "unscraped_count": 53,
  "output_file": "duanju_actors.json",
  "description": "筛选出还没有爬过的演员，准备进行批量爬取"
}
```

## 爬取进度

### 当前状态
- 爬取进程: 正在运行
- 监控进程: 正在运行
- 已完成: 4/53 (7.5%)
- 预计完成时间: 约10-15分钟（基于限流设置）

### 限流策略
- 每分钟最大5个请求
- 每个请求间隔12秒
- 每5个请求后休眠至少60秒

## 注意事项

1. **文件管理**: 所有操作相关文件都保存在独立的操作记录文件夹中
2. **断点续传**: 如果爬取中断，重新运行 `search_user_batch.py` 会自动跳过已完成的演员
3. **监控**: 可以随时运行 `python monitor_scraping_progress.py report` 生成当前进度报告
4. **限流**: 严格遵守限流策略，避免对目标服务器造成压力

## 后续操作

爬取完成后，系统会自动：
1. 生成最终报告 `scraping_final_report.json`
2. 统计成功率和失败情况
3. 更新 `search_user_results.json` 文件

如需手动生成报告：
```bash
python monitor_scraping_progress.py report
```

## 信息提取任务

### 第二阶段：演员信息提取
在爬取完成后，进行了演员信息提取任务：

#### 提取目标
1. 小红书账号 ID
2. 微博 ID
3. 微博超话 ID
4. 短剧演员的介绍
5. 抖音 ID

#### 提取策略
采用了两阶段提取策略：

**阶段1：简化版提取**
- 使用正则表达式和关键词匹配
- 从搜索结果中提取基本信息
- 汇总演员简介，去除重复内容
- 结果：成功提取64个演员的简介，但社交媒体账号提取效果不佳

**阶段2：LLM增强提取**
- 使用硅基流动的 DeepSeek-V3 模型
- 智能分析搜索结果内容
- 提取社交媒体账号信息
- 结果：显著提升了社交媒体账号的提取成功率

#### 最终提取结果
- **总处理演员数**: 64个
- **小红书账号**: 0个 (0.0%)
- **微博账号**: 11个 (17.2%)
- **微博超话**: 7个 (10.9%)
- **抖音账号**: 9个 (14.1%)
- **演员简介**: 64个 (100.0%)

#### 成功案例
- **左一**: 微博(zoe左一同学)、微博超话(左一同学)、抖音(左一同学)
- **王云云**: 抖音(彩彩云)
- **孟娜**: 微博(7871970996)
- **徐思祁**: 微博(徐思祁)、微博超话(全世界最好的徐思祁)

## 技术细节

### 筛选逻辑
- 读取 `search_user_results.json` 获取已爬取的演员ID
- 读取 `duanju_actors_need_scrape.json` 获取需要爬取的演员
- 使用集合操作筛选出未爬取的演员
- 保持原始数据格式不变

### 爬取策略
- 使用现有的 `search_user_batch.py` 脚本
- 自动处理限流和错误重试
- 实时保存结果，支持断点续传

### 信息提取技术
- **正则表达式匹配**: 基础的模式识别
- **LLM智能分析**: 使用DeepSeek-V3模型进行内容理解
- **多源信息汇总**: 整合百度百科、微博、抖音等多个来源
- **去重处理**: 确保信息的唯一性和准确性

### 监控机制
- 30秒间隔检查进度
- 实时显示完成百分比
- 记录最近完成的演员
- 自动生成最终报告
