import requests
import json
import os
import time
from glob import glob

url = "https://data.wansu.tech/api/duanju_actors:create"

payload = {
  "name": "测试",
  "platform": "ppx"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Content-Type': "application/json",
  'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
  'authorization': "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NDk3MzUxMDAyMTQsImlhdCI6MTc0OTgyMTUwMCwiZXhwIjoxNzQ5OTA3OTAwLCJqdGkiOiJmNGM1MTk3YS02Y2I3LTQ1YmMtYjhiNC00ZDFiYTJmYTA1MDcifQ.0Iwht03Mp8J9VRcaOptQhxmI60IGBlijfawy0YDzmiQ",
  'origin': "https://data.wansu.tech",
  'priority': "u=1, i",
  'referer': "https://data.wansu.tech/admin/u70167igwnz/popups/ea8eeu1ubmi",
  'sec-ch-ua': "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  'sec-ch-ua-mobile': "?0",
  'sec-ch-ua-platform': "\"macOS\"",
  'sec-fetch-dest': "empty",
  'sec-fetch-mode': "cors",
  'sec-fetch-site': "same-origin",
  'x-authenticator': "basic",
  'x-hostname': "data.wansu.tech",
  'x-locale': "zh-CN",
  'x-role': "root",
  'x-timezone': "+08:00",
  'x-with-acl-meta': "true"
}

actor_to_id = {}
bookid_to_info = {}


# 新增：读取“列表”目录下的演员信息
list_dir_path = '/Users/<USER>/Documents/短剧/皮皮虾/列表'
list_json_files = glob(os.path.join(list_dir_path, '*.json'))

for file_path in list_json_files:
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            data = json.load(f)
        except Exception as e:
            print(f'Failed to load {file_path}: {e}')
            continue
        # 只处理 data[0].feed_data.book_data
        try:
            book_data_list = data['data'][0]['feed_data']['book_data']
        except Exception as e:
            print(f'Invalid structure in {file_path}: {e}')
            continue
        for book in book_data_list:
            book_info = book.get('book_data', {})
            book_id = book_info.get('book_id')
            actor_list = book_info.get('actor_list', [])
            if not book_id or not isinstance(actor_list, list):
                continue
            for actor in actor_list:
                author = actor.get('name')
                avatar_url = actor.get('avatar_url', '')
                # 提取 avatar_id
                avatar_id = ''
                if avatar_url:
                    try:
                        avatar_id = avatar_url.split('/')[-1].split('~')[0]
                    except Exception:
                        avatar_id = ''
                if not author or not avatar_id:
                    continue
                actor_key = (author, avatar_id)
                if actor_key in actor_to_id:
                    bookid_to_info[book_id] = {"id": actor_to_id[actor_key], "name": author, "avatar_id": avatar_id}
                    continue
                payload = {
                    "name": author,
                    "platform": "ppx",
                    "avatar_url": avatar_id
                }
                response = requests.post(url, data=json.dumps(payload), headers=headers)
                try:
                    resp_json = response.json()
                    author_id = resp_json['data']['id']
                    author_name = resp_json['data']['name']
                    print(f'Success: author={author_name}, id={author_id}, avatar_id={avatar_id}')
                    actor_to_id[actor_key] = author_id
                    bookid_to_info[book_id] = {"id": author_id, "name": author_name, "avatar_id": avatar_id}
                except Exception as e:
                    print(f'Failed to parse response for {author}: {e}\n{response.text}')

# 可选：输出两个 map 检查
with open('/Users/<USER>/python/daunju_actors.bookid_to_info.json', 'w', encoding='utf-8') as f:
    json.dump(bookid_to_info, f, ensure_ascii=False, indent=2)
with open('/Users/<USER>/python/daunju_actors.actor_to_id.json', 'w', encoding='utf-8') as f:
    # 需要将 tuple key 转为字符串存储
    json.dump({f"{k[0]}|{k[1]}": v for k, v in actor_to_id.items()}, f, ensure_ascii=False, indent=2)
print('bookid_to_info and actor_to_id have been saved to /Users/<USER>/python/')