#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于验证数据库连接是否正常
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'yuanshu@nb',
    'database': 'huawaiyin',
    'charset': 'utf8mb4'
}

def test_connection():
    """测试数据库连接"""
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logger.info("数据库连接成功！")
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) FROM duanju_actors")
        count = cursor.fetchone()[0]
        logger.info(f"duanju_actors 表中共有 {count} 条记录")
        
        # 查询表结构
        cursor.execute("DESCRIBE duanju_actors")
        columns = cursor.fetchall()
        logger.info("表结构:")
        for column in columns:
            logger.info(f"  {column[0]} - {column[1]}")
        
        # 关闭连接
        cursor.close()
        connection.close()
        logger.info("数据库连接已关闭")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("开始测试数据库连接...")
    success = test_connection()
    if success:
        logger.info("数据库连接测试通过！")
    else:
        logger.error("数据库连接测试失败！")
