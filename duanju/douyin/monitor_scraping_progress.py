#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控爬取进度脚本
"""

import json
import os
import time
from datetime import datetime

def load_target_actors():
    """加载目标演员列表"""
    try:
        with open("duanju_actors.json", "r", encoding="utf-8") as f:
            actors = json.load(f)
            return {str(actor["id"]): actor["name"] for actor in actors}
    except Exception as e:
        print(f"读取目标演员列表失败: {e}")
        return {}

def load_current_results():
    """加载当前爬取结果"""
    try:
        with open("search_user_results.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"读取爬取结果失败: {e}")
        return {}

def monitor_progress():
    """监控爬取进度"""
    target_actors = load_target_actors()
    if not target_actors:
        print("没有找到目标演员列表")
        return
    
    total_count = len(target_actors)
    print(f"目标演员总数: {total_count}")
    print("=" * 50)
    
    while True:
        current_results = load_current_results()
        completed_count = 0
        completed_actors = []
        
        for actor_id, actor_name in target_actors.items():
            if actor_id in current_results:
                completed_count += 1
                completed_actors.append(f"{actor_name}({actor_id})")
        
        progress_percent = (completed_count / total_count) * 100
        remaining_count = total_count - completed_count
        
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 爬取进度:")
        print(f"  已完成: {completed_count}/{total_count} ({progress_percent:.1f}%)")
        print(f"  剩余: {remaining_count}")
        
        if completed_actors:
            print(f"  最近完成的演员: {', '.join(completed_actors[-5:])}")
        
        if completed_count >= total_count:
            print("\n🎉 所有演员爬取完成！")
            break
        
        print("  等待30秒后再次检查...")
        time.sleep(30)

def create_final_report():
    """创建最终报告"""
    target_actors = load_target_actors()
    current_results = load_current_results()
    
    completed = []
    failed = []
    
    for actor_id, actor_name in target_actors.items():
        if actor_id in current_results:
            result = current_results[actor_id]
            if result.get("success", False):
                completed.append({"id": actor_id, "name": actor_name, "status": "success"})
            else:
                completed.append({"id": actor_id, "name": actor_name, "status": "failed"})
        else:
            failed.append({"id": actor_id, "name": actor_name, "status": "not_scraped"})
    
    report = {
        "report_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_target_count": len(target_actors),
        "completed_count": len(completed),
        "failed_count": len(failed),
        "success_rate": f"{(len(completed) / len(target_actors) * 100):.1f}%" if target_actors else "0%",
        "completed_actors": completed,
        "failed_actors": failed
    }
    
    # 保存到操作记录文件夹
    operations_path = "../../operations"
    if os.path.exists(operations_path):
        operation_dirs = [d for d in os.listdir(operations_path) if d.startswith("douyin_actor_scrape_")]
        if operation_dirs:
            latest_operation_dir = os.path.join(operations_path, sorted(operation_dirs)[-1])
            report_file = os.path.join(latest_operation_dir, "scraping_final_report.json")
            try:
                with open(report_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                print(f"最终报告已保存到: {report_file}")
            except Exception as e:
                print(f"保存最终报告失败: {e}")
    
    return report

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "report":
        # 生成最终报告
        print("生成最终报告...")
        report = create_final_report()
        print(f"总目标数: {report['total_target_count']}")
        print(f"已完成数: {report['completed_count']}")
        print(f"失败数: {report['failed_count']}")
        print(f"成功率: {report['success_rate']}")
    else:
        # 监控进度
        try:
            monitor_progress()
            # 爬取完成后生成最终报告
            create_final_report()
        except KeyboardInterrupt:
            print("\n监控已停止")
            print("如需生成当前报告，请运行: python monitor_scraping_progress.py report")
