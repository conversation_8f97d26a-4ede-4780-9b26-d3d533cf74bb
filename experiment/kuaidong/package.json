{"name": "kuaidong-baike-search", "version": "1.0.0", "description": "使用 Playwright 模拟用户输入获取百科网站搜索建议数据", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "search": "node src/cli.js", "install-browsers": "npx playwright install", "test": "node src/test.js"}, "keywords": ["playwright", "web-scraping", "baike", "search", "automation"], "author": "", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "commander": "^11.1.0", "chalk": "^5.3.0"}, "devDependencies": {"@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0"}}