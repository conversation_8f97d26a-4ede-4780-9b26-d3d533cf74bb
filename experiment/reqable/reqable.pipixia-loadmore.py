# API Docs: https://reqable.com/docs/capture/addons

from reqable import *
import os

def onRequest(context, request):
  # Done
  return request

def onResponse(context, response):
  # 打印请求信息，更多API请参考上文`HttpRequest`
  # print(response.request)
  request = response.request
  selected_items = request.queries['selected_items']
  batch = request.queries['batch_n']
  print(f"Batch Number: {batch}, Selected Items: {selected_items}")
  
  # print(response.body)

  directory = "/Users/<USER>/Documents/短剧/皮皮虾/列表"
  os.makedirs(directory, exist_ok=True)
  filename = os.path.join(directory, f"{selected_items}-{batch}.json")
  with open(filename, "wb") as f:
      if isinstance(response.body, bytes):
          f.write(response.body)
      else:
          f.write(str(response.body).encode("utf-8"))

  # 新增：解析JSON并下载封面图
  import json
  import requests
  cover_dir = "/Users/<USER>/Documents/短剧/皮皮虾/封面图"
  os.makedirs(cover_dir, exist_ok=True)
  try:
      if isinstance(response.body, bytes):
          body_json = json.loads(response.body.decode("utf-8"))
      else:
          body_json = json.loads(str(response.body))
      data0 = body_json.get("data", [])[0]
      feed_data = data0.get("feed_data", {})
      book_data_list = feed_data.get("book_data", [])
      for item in book_data_list:
          book = item.get("book_data", {})
          thumb_url = book.get("thumb_url")
          book_id = book.get("book_id")
          if thumb_url and book_id:
              try:
                  resp = requests.get(thumb_url, timeout=10, verify="/Users/<USER>/Documents/短剧/reqable-ca.crt")
                  if resp.status_code == 200:
                      cover_path = os.path.join(cover_dir, f"{book_id}.webp")
                      with open(cover_path, "wb") as imgf:
                          imgf.write(resp.content)
              except Exception as e:
                  print(f"Download error for {thumb_url}: {e}")
  except Exception as e:
      print(f"JSON parse or download error: {e}")

  # 修改响应状态码
  # response.code = 400

  # 更多的示例参考上面的`onRequest`，完全一样。

  # Done
  return response

