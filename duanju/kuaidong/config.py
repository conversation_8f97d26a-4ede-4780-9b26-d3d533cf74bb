"""
批量上传处理器配置文件
"""

# API配置
API_CONFIG = {
    'base_url': 'https://data.wansu.tech/api',
    'auth_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInRlbXAiOnRydWUsInNpZ25JblRpbWUiOjE3NTMwOTA5Njk2ODEsImlhdCI6MTc1MzE3NzQyMywiZXhwIjoxNzUzMjYzODIzLCJqdGkiOiJkOGZjYzBlNC1lNDdmLTRiMTAtYTkwMS04YmM0MzYzMDUzNWQifQ.HUMaYaD6WwAWJrFNcvcKYGl90V8Aiij_EqsTSAhGiB0',
    'timeout': 300,  # 上传超时时间（秒）
}

# 文件路径配置
FILE_CONFIG = {
    'data_dir': 'kuaidong_data',
    'temp_dir': 'temp',
    'actors_file': 'duanju_actors_with_kuaidong.json',
    'log_file': 'batch_upload.log'
}

# 处理配置
PROCESS_CONFIG = {
    'delay_between_actors': 1,  # 处理每个演员之间的延迟（秒）
    'retry_count': 3,  # API调用失败时的重试次数
    'retry_delay': 5,  # 重试之间的延迟（秒）
}

# Cut创建默认参数
CUT_DEFAULTS = {
    'desc': '描述',
    'title_template': '{actor_name} - Cut 1',  # {actor_name} 会被替换为演员姓名
    'play_count': 10,
    'duration': 20
}

# FFmpeg配置
FFMPEG_CONFIG = {
    'extract_frame_cmd': [
        'ffmpeg', '-i', '{input}',
        '-vframes', '1',
        '-f', 'image2',
        '-y',  # 覆盖输出文件
        '{output}'
    ]
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file_enabled': True,
    'console_enabled': True
}
