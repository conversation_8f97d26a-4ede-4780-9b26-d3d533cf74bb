# 增强版批量上传处理脚本功能说明

## 新增功能

### 1. 头像上传功能
- **自动查找头像文件**：在每个演员的 `images/` 目录下查找 `*_head_*.png/jpg/jpeg` 文件
- **批量上传头像**：上传演员文件夹中的所有头像文件，而不是单个
- **API接口**：使用 `duanju_actors.head_images` 字段上传头像

### 2. 演员信息更新
- **更新头像信息**：上传成功后自动更新演员的 `head_images` 字段
- **保留现有数据**：只更新头像字段，不影响其他演员信息

### 3. 视频处理增强
- **处理所有视频**：支持上传演员文件夹中的所有视频文件，而不是单个
- **灵活配置**：可选择处理第一个视频或所有视频

### 4. 操作记录管理
- **统一目录**：所有操作记录保存到 `operations_records/` 目录
- **详细日志**：包含头像上传、视频处理、演员更新的完整记录
- **统计报告**：生成简化的统计报告文件

## 使用方法

### 方法1：使用增强版运行脚本（推荐）
```bash
python run_enhanced_batch_upload.py
```

### 方法2：直接调用处理器
```python
from batch_upload_processor import BatchUploadProcessor

processor = BatchUploadProcessor()

# 只处理头像
processor.process_all_actors(
    process_all_videos=False,
    process_head_images=True,
    only_with_videos=False
)

# 处理头像和所有视频
processor.process_all_actors(
    process_all_videos=True,
    process_head_images=True,
    only_with_videos=False
)
```

## 配置参数说明

### process_all_actors 方法参数
- `start_index`: 开始处理的演员索引（默认0）
- `end_index`: 结束处理的演员索引（默认None，处理到最后）
- `process_all_videos`: 是否处理所有视频（默认False，只处理第一个）
- `process_head_images`: 是否处理头像（默认True）
- `only_with_videos`: 是否只处理有视频的演员（默认False）

## 数据结构要求

### 演员数据目录结构
```
kuaidong_data/
├── {kuaidong_id}/
│   ├── images/
│   │   ├── {kuaidong_id}_head_0.png
│   │   ├── {kuaidong_id}_head_1.png
│   │   └── ...
│   └── videos/
│       ├── {kuaidong_id}_video_0.mp4
│       ├── {kuaidong_id}_video_1.mp4
│       └── ...
```

## API接口

### 头像上传接口
```
POST /api/attachments:create?attachmentField=duanju_actors.head_images
Content-Type: multipart/form-data
```

### 演员信息更新接口
```
PATCH /api/duanju_actors:update?filterByTk={actor_id}
Content-Type: application/json
{
  "head_images": [...]
}
```

## 输出文件

### 操作记录目录结构
```
operations_records/
├── batch_upload_results_{timestamp}.json  # 详细处理结果
└── batch_upload_stats_{timestamp}.txt     # 简化统计报告
```

### 结果数据结构
```json
{
  "total_processed": 100,
  "successful": 95,
  "failed": 5,
  "total_cuts_created": 150,
  "total_head_images_uploaded": 380,
  "processed_at": "2025-01-22T15:30:00",
  "results": [
    {
      "actor_id": "33",
      "kuaidong_id": "7387251624085438474",
      "actor_name": "王小亿",
      "success": true,
      "cuts_created": [...],
      "head_images_uploaded": [...],
      "head_images_update_result": {...}
    }
  ]
}
```

## 注意事项

1. **API Token**：确保 `auth_token` 有效且有足够权限
2. **文件格式**：支持 PNG、JPG、JPEG 格式的头像文件
3. **网络稳定**：上传大量文件时确保网络连接稳定
4. **API限流**：脚本已内置延迟机制，避免API限流
5. **错误处理**：单个文件失败不会影响其他文件的处理

## 故障排除

### 常见问题
1. **头像上传失败**：检查文件格式和网络连接
2. **演员信息更新失败**：检查actor_id是否正确
3. **找不到文件**：检查目录结构是否正确
4. **API权限错误**：检查token是否有效

### 日志查看
- 控制台输出：实时查看处理进度
- `batch_upload.log`：详细的处理日志
- `operations_records/`：完整的操作记录
