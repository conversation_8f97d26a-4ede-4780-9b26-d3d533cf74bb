"""
52kanduanju.com 数据模型定义
"""

from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any
import json
from datetime import datetime


@dataclass
class Actor:
    """演员数据模型"""
    name: str
    actor_id: Optional[str] = None
    actor_url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Drama:
    """短剧数据模型"""
    drama_id: str
    title: str
    description: Optional[str] = None
    episodes: Optional[int] = None
    company: Optional[str] = None
    actors: List[str] = None
    categories: List[str] = None
    cover_url: Optional[str] = None
    detail_url: Optional[str] = None
    is_hot: bool = False
    crawled_at: Optional[str] = None
    
    def __post_init__(self):
        if self.actors is None:
            self.actors = []
        if self.categories is None:
            self.categories = []
        if self.crawled_at is None:
            self.crawled_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Category:
    """分类数据模型"""
    name: str
    slug: str
    count: Optional[int] = None
    url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class DataManager:
    """数据管理器"""
    
    def __init__(self, dramas_file: str, actors_file: str, categories_file: str):
        self.dramas_file = dramas_file
        self.actors_file = actors_file
        self.categories_file = categories_file
        
        # 内存中的数据
        self.dramas: Dict[str, Drama] = {}
        self.actors: Dict[str, Actor] = {}
        self.categories: Dict[str, Category] = {}
    
    def add_drama(self, drama: Drama) -> None:
        """添加短剧数据"""
        self.dramas[drama.drama_id] = drama
    
    def add_actor(self, actor: Actor) -> None:
        """添加演员数据"""
        key = actor.actor_id if actor.actor_id else actor.name
        self.actors[key] = actor
    
    def add_category(self, category: Category) -> None:
        """添加分类数据"""
        self.categories[category.slug] = category
    
    def get_drama(self, drama_id: str) -> Optional[Drama]:
        """获取短剧数据"""
        return self.dramas.get(drama_id)
    
    def get_actor(self, key: str) -> Optional[Actor]:
        """获取演员数据"""
        return self.actors.get(key)
    
    def get_category(self, slug: str) -> Optional[Category]:
        """获取分类数据"""
        return self.categories.get(slug)
    
    def save_to_json(self) -> None:
        """保存数据到JSON文件"""
        # 保存短剧数据
        dramas_data = [drama.to_dict() for drama in self.dramas.values()]
        with open(self.dramas_file, 'w', encoding='utf-8') as f:
            json.dump(dramas_data, f, ensure_ascii=False, indent=2)
        
        # 保存演员数据
        actors_data = [actor.to_dict() for actor in self.actors.values()]
        with open(self.actors_file, 'w', encoding='utf-8') as f:
            json.dump(actors_data, f, ensure_ascii=False, indent=2)
        
        # 保存分类数据
        categories_data = [category.to_dict() for category in self.categories.values()]
        with open(self.categories_file, 'w', encoding='utf-8') as f:
            json.dump(categories_data, f, ensure_ascii=False, indent=2)
    
    def load_from_json(self) -> None:
        """从JSON文件加载数据"""
        try:
            # 加载短剧数据
            with open(self.dramas_file, 'r', encoding='utf-8') as f:
                dramas_data = json.load(f)
                for data in dramas_data:
                    drama = Drama(**data)
                    self.dramas[drama.drama_id] = drama
        except FileNotFoundError:
            pass
        
        try:
            # 加载演员数据
            with open(self.actors_file, 'r', encoding='utf-8') as f:
                actors_data = json.load(f)
                for data in actors_data:
                    actor = Actor(**data)
                    key = actor.actor_id if actor.actor_id else actor.name
                    self.actors[key] = actor
        except FileNotFoundError:
            pass
        
        try:
            # 加载分类数据
            with open(self.categories_file, 'r', encoding='utf-8') as f:
                categories_data = json.load(f)
                for data in categories_data:
                    category = Category(**data)
                    self.categories[category.slug] = category
        except FileNotFoundError:
            pass
    
    def get_stats(self) -> Dict[str, int]:
        """获取数据统计信息"""
        return {
            "dramas_count": len(self.dramas),
            "actors_count": len(self.actors),
            "categories_count": len(self.categories)
        }
