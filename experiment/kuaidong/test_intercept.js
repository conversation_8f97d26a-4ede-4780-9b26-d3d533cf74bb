#!/usr/bin/env node

import { BaikeSearcher } from './src/searcher.js';

async function testIntercept() {
  console.log('🧪 测试API请求拦截');
  console.log('=' * 50);
  
  const searcher = new BaikeSearcher({
    headless: false, // 显示浏览器以便观察
    timeout: 30000
  });

  try {
    console.log('🔍 测试关键词: 王小亿');
    
    const result = await searcher.search('王小亿');
    
    console.log('\n✅ 拦截成功!');
    console.log('📊 拦截结果:', JSON.stringify(result, null, 2));
    
    if (result.intercepted) {
      console.log('\n🎯 成功提取的关键参数:');
      console.log(`   msToken: ${result.msToken}`);
      console.log(`   a_bogus: ${result.aBogus}`);
      console.log(`   完整URL: ${result.url}`);
      
      if (result.postData) {
        console.log(`   POST请求体: ${result.postData}`);
        
        try {
          const postJson = JSON.parse(result.postData);
          console.log('   解析后的POST数据:', JSON.stringify(postJson, null, 2));
        } catch (e) {
          console.log('   POST数据不是JSON格式');
        }
      }
      
      console.log('\n🎉 任务完成! 已成功拦截并提取参数');
    } else {
      console.log('\n❌ 拦截失败');
    }
    
  } catch (error) {
    console.log('\n❌ 测试失败:', error.message);
  } finally {
    await searcher.close();
  }
}

// 运行测试
testIntercept().catch(console.error);
