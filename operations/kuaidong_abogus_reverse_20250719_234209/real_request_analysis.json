{"timestamp": 1752940319.557976, "real_abogus": "Y6UQXc2aMsm1r8leBXkz9SaovZm0YW5IgZENcvvqGtw6", "real_analysis": {"length": 44, "charset": ["W", "q", "8", "0", "E", "S", "Y", "z", "M", "a", "g", "c", "w", "v", "Z", "t", "G", "I", "9", "Q", "6", "N", "o", "l", "U", "s", "k", "5", "2", "r", "X", "m", "e", "1", "B"], "has_uppercase": true, "has_lowercase": true, "has_digits": true, "has_special": false, "base64_like": true, "possible_encoding": ["Base64 -> 63a5105dcd9a32c9b5afc95e057933f526a8bd99b4616e4881910d72fbea1adc3a", "URL-Safe Base64 -> 63a5105dcd9a32c9b5afc95e057933f526a8bd99b4616e4881910d72fbea1adc3a"]}, "decoded_results": [["Base64", "63a5105dcd9a32c9b5afc95e057933f526a8bd99b4616e4881910d72fbea1adc3a"], ["URL-Safe Base64", "63a5105dcd9a32c9b5afc95e057933f526a8bd99b4616e4881910d72fbea1adc3a"]], "improved_results": {"method1_sha256_b64": "t4U/Hfkl34gQn7kB0OyKq0wYMl9yNpGBYVyJH0lJH8A", "method2_md5_urlsafe": "fojd2Z-CYRFqolNz8an8mg", "method3_sha1_mixed": "ZGRhZTQwZGY3ZjdkZWYxZWMyZGNkNjEwZmVmNWZkMzU=", "timestamp": 1752940319557}, "real_response_success": false}